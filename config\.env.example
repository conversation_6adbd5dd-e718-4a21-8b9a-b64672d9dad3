# Food Monitoring System - Environment Configuration Template
# Copy this file to .env.development, .env.testing, or .env.production
# and customize the values for your environment

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Flask environment (development, testing, production)
FLASK_ENV=development

# Secret key for session management (CHANGE THIS IN PRODUCTION!)
SECRET_KEY=your-secret-key-here

# Application debug mode
FLASK_DEBUG=true

# =============================================================================
# DATABASE SETTINGS
# =============================================================================

# Database URL - SQLite for development, PostgreSQL for production
DATABASE_URL=sqlite:///data/databases/monitoring.db

# Database connection pool settings
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# JWT settings for API authentication
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ACCESS_TOKEN_EXPIRES=3600
JWT_REFRESH_TOKEN_EXPIRES=2592000

# Password hashing rounds
BCRYPT_LOG_ROUNDS=12

# =============================================================================
# EMAIL NOTIFICATION SETTINGS
# =============================================================================

# SMTP server configuration
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>

# Default alert recipients (comma-separated)
DEFAULT_ALERT_RECIPIENTS=<EMAIL>,<EMAIL>

# =============================================================================
# MONITORING SETTINGS
# =============================================================================

# Data retention periods (in days)
SENSOR_DATA_RETENTION_DAYS=90
ALERT_RETENTION_DAYS=365

# System health check interval (in seconds)
HEALTH_CHECK_INTERVAL=300

# Default monitoring thresholds
DEFAULT_TEMP_MIN=-2.0
DEFAULT_TEMP_MAX=4.0
DEFAULT_HUMIDITY_MIN=80.0
DEFAULT_HUMIDITY_MAX=95.0

# Alert settings
ALERT_ESCALATION_ENABLED=true
ALERT_COOLDOWN_MINUTES=15

# =============================================================================
# REDIS SETTINGS (for caching and background tasks)
# =============================================================================

REDIS_URL=redis://localhost:6379/0
CACHE_DEFAULT_TIMEOUT=300

# =============================================================================
# CELERY SETTINGS (for background tasks)
# =============================================================================

CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# =============================================================================
# LOGGING SETTINGS
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Log file path
LOG_FILE=logs/app.log

# Log rotation settings
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=5

# =============================================================================
# API SETTINGS
# =============================================================================

# API rate limiting
API_RATE_LIMIT=1000 per hour

# API pagination
API_PAGINATION_DEFAULT=20
API_PAGINATION_MAX=100

# =============================================================================
# FILE UPLOAD SETTINGS
# =============================================================================

# Maximum file upload size (in bytes)
MAX_CONTENT_LENGTH=********

# Upload directory
UPLOAD_FOLDER=data/uploads

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# Sentry DSN for error tracking (production only)
SENTRY_DSN=

# Webhook URLs for external notifications
SLACK_WEBHOOK_URL=
TEAMS_WEBHOOK_URL=

# SMS service settings (Twilio example)
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Enable/disable features for development
ENABLE_SENSOR_SIMULATION=true
ENABLE_DEBUG_TOOLBAR=true
ENABLE_PROFILER=false

# Development database seeding
SEED_SAMPLE_DATA=true

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================

# Production-specific settings (uncomment for production)
# FLASK_ENV=production
# FLASK_DEBUG=false
# DATABASE_URL=postgresql://user:password@localhost/monitoring_prod
# SESSION_COOKIE_SECURE=true
# WTF_CSRF_ENABLED=true
