#!/usr/bin/env python3
"""
Test script to verify the Food Monitoring System components
"""

import sys
import os
import traceback
from datetime import datetime

# Add the monitoring_app directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database():
    """Test database functionality"""
    print("Testing database module...")
    try:
        from database import DatabaseManager
        db = DatabaseManager()

        # Test inserting sensor data
        test_data = {
            'device_id': 'TEST_SENSOR',
            'temperature': 4.5,
            'humidity': 85.0,
            'location': 'Test Location',
            'product_type': 'Fresh Vegetables',
            'batch_id': 'TEST_BATCH_001',
            'timestamp': datetime.now().isoformat()
        }

        success = db.insert_sensor_data(test_data)
        if success:
            print("✓ Database: Sensor data insertion successful")
        else:
            print("✗ Database: Sensor data insertion failed")

        # Test getting sensor data
        data = db.get_sensor_data('TEST_SENSOR', hours=1)
        if data:
            print(f"✓ Database: Retrieved {len(data)} sensor readings")
        else:
            print("✗ Database: No sensor data retrieved")

        # Test statistics
        stats = db.get_statistics()
        print(f"✓ Database: Statistics - {stats.get('total_readings', 0)} total readings")

        return True

    except Exception as e:
        print(f"✗ Database test failed: {e}")
        traceback.print_exc()
        return False

def test_monitoring_core():
    """Test monitoring core functionality"""
    print("\nTesting monitoring core...")
    try:
        from monitoring_core import MonitoringCore
        core = MonitoringCore()

        # Test configuration loading
        if core.config:
            print("✓ Monitoring Core: Configuration loaded")
        else:
            print("✗ Monitoring Core: Configuration not loaded")

        # Test system status
        status = core.get_system_status()
        if status:
            print(f"✓ Monitoring Core: System status retrieved - {status.get('total_sensors', 0)} sensors")
        else:
            print("✗ Monitoring Core: System status failed")

        return True

    except Exception as e:
        print(f"✗ Monitoring Core test failed: {e}")
        traceback.print_exc()
        return False

def test_sensor_simulator():
    """Test sensor simulator"""
    print("\nTesting sensor simulator...")
    try:
        from sensor_simulator import SensorSimulator

        # Create a test simulator
        simulator = SensorSimulator('TEST_SIM_001', 'http://localhost:5000/api/data', 'Test Location')

        # Test data generation
        data = simulator.generate_data()
        if data and 'temperature' in data and 'humidity' in data:
            print(f"✓ Sensor Simulator: Generated data - T={data['temperature']}°C, H={data['humidity']}%")
        else:
            print("✗ Sensor Simulator: Data generation failed")

        return True

    except Exception as e:
        print(f"✗ Sensor Simulator test failed: {e}")
        traceback.print_exc()
        return False

def test_flask_app():
    """Test Flask application"""
    print("\nTesting Flask application...")
    try:
        from app import app

        # Test app creation
        if app:
            print("✓ Flask App: Application created successfully")
        else:
            print("✗ Flask App: Application creation failed")

        # Test with test client
        with app.test_client() as client:
            # Test dashboard route
            response = client.get('/')
            if response.status_code == 200:
                print("✓ Flask App: Dashboard route accessible")
            else:
                print(f"✗ Flask App: Dashboard route failed - Status {response.status_code}")

            # Test API route
            test_data = {
                'device_id': 'API_TEST',
                'temperature': 3.2,
                'humidity': 82.5,
                'location': 'API Test Location',
                'timestamp': datetime.now().isoformat()
            }

            response = client.post('/api/data', json=test_data)
            if response.status_code == 200:
                print("✓ Flask App: API data endpoint working")
            else:
                print(f"✗ Flask App: API data endpoint failed - Status {response.status_code}")

        return True

    except Exception as e:
        print(f"✗ Flask App test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🍎 Food Monitoring System - Component Tests")
    print("=" * 50)

    tests = [
        test_database,
        test_monitoring_core,
        test_sensor_simulator,
        test_flask_app
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"Test failed with exception: {e}")

    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! The system is ready to run.")
        print("\nTo start the system:")
        print("python main.py --init --simulate")
        print("\nThen open: http://localhost:5000")
    else:
        print("❌ Some tests failed. Please check the errors above.")

    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)