import requests
import random
import time
import threading
import logging
from datetime import datetime
from typing import Dict, List

class SensorSimulator:
    """Enhanced sensor simulator for testing the monitoring system"""

    def __init__(self, device_id: str, api_url: str, location: str = "Warehouse A",
                 product_type: str = "Fresh Vegetables", batch_id: str = None):
        self.device_id = device_id
        self.api_url = api_url
        self.location = location
        self.product_type = product_type
        self.batch_id = batch_id or f"BATCH_{device_id}_{datetime.now().strftime('%Y%m%d')}"
        self.running = False
        self.thread = None

        # Simulation parameters
        self.base_temp = 4.0  # Base temperature
        self.base_humidity = 85.0  # Base humidity
        self.temp_variance = 2.0  # Temperature variance
        self.humidity_variance = 5.0  # Humidity variance
        self.drift_factor = 0.1  # Gradual drift factor
        self.anomaly_chance = 0.05  # 5% chance of anomaly

        # Current state
        self.current_temp = self.base_temp
        self.current_humidity = self.base_humidity

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(f"Sensor_{device_id}")

    def set_product_profile(self, product_type: str):
        """Set sensor parameters based on product type"""
        profiles = {
            'Fresh Meat': {
                'base_temp': 2.0,
                'base_humidity': 85.0,
                'temp_variance': 1.5,
                'humidity_variance': 3.0
            },
            'Dairy Products': {
                'base_temp': 3.0,
                'base_humidity': 80.0,
                'temp_variance': 1.0,
                'humidity_variance': 2.0
            },
            'Fresh Vegetables': {
                'base_temp': 4.0,
                'base_humidity': 85.0,
                'temp_variance': 2.0,
                'humidity_variance': 5.0
            },
            'Frozen Foods': {
                'base_temp': -20.0,
                'base_humidity': 80.0,
                'temp_variance': 2.0,
                'humidity_variance': 5.0
            },
            'Fruits': {
                'base_temp': 6.0,
                'base_humidity': 85.0,
                'temp_variance': 2.5,
                'humidity_variance': 4.0
            }
        }

        if product_type in profiles:
            profile = profiles[product_type]
            self.base_temp = profile['base_temp']
            self.base_humidity = profile['base_humidity']
            self.temp_variance = profile['temp_variance']
            self.humidity_variance = profile['humidity_variance']
            self.product_type = product_type

            # Reset current values
            self.current_temp = self.base_temp
            self.current_humidity = self.base_humidity

            self.logger.info(f"Set product profile to {product_type}")

    def generate_data(self) -> Dict:
        """Generate realistic sensor data with gradual changes and occasional anomalies"""

        # Generate gradual drift
        temp_drift = random.uniform(-self.drift_factor, self.drift_factor)
        humidity_drift = random.uniform(-self.drift_factor, self.drift_factor)

        # Apply drift to current values
        self.current_temp += temp_drift
        self.current_humidity += humidity_drift

        # Add random variance
        temp_variance = random.uniform(-self.temp_variance, self.temp_variance)
        humidity_variance = random.uniform(-self.humidity_variance, self.humidity_variance)

        temperature = self.current_temp + temp_variance
        humidity = self.current_humidity + humidity_variance

        # Simulate occasional anomalies
        if random.random() < self.anomaly_chance:
            if random.choice([True, False]):
                # Temperature anomaly
                temperature += random.uniform(-10, 15)
                self.logger.warning(f"Simulating temperature anomaly: {temperature:.2f}°C")
            else:
                # Humidity anomaly
                humidity += random.uniform(-20, 25)
                self.logger.warning(f"Simulating humidity anomaly: {humidity:.2f}%")

        # Keep values within reasonable bounds
        temperature = max(-30, min(50, temperature))
        humidity = max(0, min(100, humidity))

        # Gradually return to base values
        self.current_temp += (self.base_temp - self.current_temp) * 0.01
        self.current_humidity += (self.base_humidity - self.current_humidity) * 0.01

        return {
            'device_id': self.device_id,
            'temperature': round(temperature, 2),
            'humidity': round(humidity, 2),
            'location': self.location,
            'product_type': self.product_type,
            'batch_id': self.batch_id,
            'timestamp': datetime.now().isoformat()
        }

    def send_data(self) -> bool:
        """Send sensor data to the API"""
        try:
            data = self.generate_data()
            response = requests.post(self.api_url, json=data, timeout=10)

            if response.status_code == 200:
                self.logger.debug(f"Data sent: T={data['temperature']}°C, H={data['humidity']}%")
                return True
            else:
                self.logger.error(f"Failed to send data: HTTP {response.status_code}")
                return False

        except requests.exceptions.RequestException as e:
            self.logger.error(f"Network error sending data: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Error sending data: {e}")
            return False

    def start_simulation(self, interval: int = 30):
        """Start continuous simulation"""
        if self.running:
            self.logger.warning("Simulation is already running")
            return

        self.running = True
        self.thread = threading.Thread(target=self._simulation_loop, args=(interval,), daemon=True)
        self.thread.start()
        self.logger.info(f"Started simulation with {interval}s interval")

    def stop_simulation(self):
        """Stop the simulation"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        self.logger.info("Stopped simulation")

    def _simulation_loop(self, interval: int):
        """Main simulation loop"""
        while self.running:
            try:
                self.send_data()
                time.sleep(interval)
            except Exception as e:
                self.logger.error(f"Error in simulation loop: {e}")
                time.sleep(5)  # Wait before retrying

class MultiSensorSimulator:
    """Manage multiple sensor simulators"""

    def __init__(self, api_url: str = 'http://localhost:5000/api/data'):
        self.api_url = api_url
        self.simulators: List[SensorSimulator] = []
        self.logger = logging.getLogger("MultiSensorSimulator")

    def add_sensor(self, device_id: str, location: str, product_type: str = "Fresh Vegetables") -> SensorSimulator:
        """Add a new sensor simulator"""
        simulator = SensorSimulator(device_id, self.api_url, location, product_type)
        simulator.set_product_profile(product_type)
        self.simulators.append(simulator)
        self.logger.info(f"Added sensor {device_id} at {location} for {product_type}")
        return simulator

    def start_all(self, interval: int = 30):
        """Start all sensor simulations"""
        for simulator in self.simulators:
            simulator.start_simulation(interval)
        self.logger.info(f"Started {len(self.simulators)} sensor simulations")

    def stop_all(self):
        """Stop all sensor simulations"""
        for simulator in self.simulators:
            simulator.stop_simulation()
        self.logger.info("Stopped all sensor simulations")

if __name__ == '__main__':
    # Create multiple sensor simulators for testing
    multi_sim = MultiSensorSimulator()

    # Add various sensors
    multi_sim.add_sensor('SENSOR_001', 'Cold Storage Room A', 'Fresh Meat')
    multi_sim.add_sensor('SENSOR_002', 'Dairy Refrigerator', 'Dairy Products')
    multi_sim.add_sensor('SENSOR_003', 'Vegetable Storage', 'Fresh Vegetables')
    multi_sim.add_sensor('SENSOR_004', 'Freezer Unit 1', 'Frozen Foods')
    multi_sim.add_sensor('SENSOR_005', 'Fruit Storage', 'Fruits')

    try:
        # Start all simulations
        multi_sim.start_all(interval=30)

        print("Sensor simulation started. Press Ctrl+C to stop...")

        # Keep the main thread alive
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        print("\nStopping sensor simulations...")
        multi_sim.stop_all()
        print("All simulations stopped.")
