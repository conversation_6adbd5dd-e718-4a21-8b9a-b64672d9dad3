#!/usr/bin/env python3
"""
Alerts Manager Backend
Handles alert creation, management, notifications, and escalation
"""

import sqlite3
import smtplib
import logging
import threading
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON><PERSON>art
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AlertSeverity(Enum):
    """Alert severity levels"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class AlertType(Enum):
    """Alert types"""
    TEMPERATURE_HIGH = "TEMPERATURE_HIGH"
    TEMPERATURE_LOW = "TEMPERATURE_LOW"
    HUMIDITY_HIGH = "HUMIDITY_HIGH"
    HUMIDITY_LOW = "HUMIDITY_LOW"
    SENSOR_OFFLINE = "SENSOR_OFFLINE"
    SENSOR_ERROR = "SENSOR_ERROR"
    SYSTEM_ERROR = "SYSTEM_ERROR"
    MAINTENANCE_DUE = "MAINTENANCE_DUE"

@dataclass
class Alert:
    """Alert data class"""
    device_id: str
    alert_type: AlertType
    severity: AlertSeverity
    message: str
    current_value: Optional[float] = None
    threshold_value: Optional[float] = None
    location: str = ""
    product_type: str = ""
    is_acknowledged: bool = False
    acknowledged_by: str = ""
    acknowledged_at: Optional[str] = None
    created_at: str = ""
    escalated: bool = False
    escalated_at: Optional[str] = None
    notification_sent: bool = False
    metadata: Dict = None

class AlertsManager:
    """Comprehensive alerts management system"""

    def __init__(self, db_path: str = "food_monitoring.db"):
        self.db_path = db_path
        self.init_database()
        self._notification_queue = []
        self._notification_thread = None
        self._running = False
        self._alert_cooldowns = {}  # Track alert cooldowns

    def init_database(self):
        """Initialize alerts database tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Enhanced alerts table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_id TEXT NOT NULL,
                    alert_type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    message TEXT NOT NULL,
                    current_value REAL,
                    threshold_value REAL,
                    location TEXT,
                    product_type TEXT,
                    is_acknowledged BOOLEAN DEFAULT 0,
                    acknowledged_by TEXT,
                    acknowledged_at DATETIME,
                    escalated BOOLEAN DEFAULT 0,
                    escalated_at DATETIME,
                    notification_sent BOOLEAN DEFAULT 0,
                    metadata TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Alert rules table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alert_rules (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    rule_name TEXT UNIQUE NOT NULL,
                    device_id TEXT,
                    alert_type TEXT NOT NULL,
                    condition_field TEXT NOT NULL,
                    condition_operator TEXT NOT NULL,
                    condition_value REAL NOT NULL,
                    severity TEXT NOT NULL,
                    cooldown_minutes INTEGER DEFAULT 15,
                    escalation_minutes INTEGER DEFAULT 60,
                    notification_emails TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Alert notifications log
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alert_notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    alert_id INTEGER NOT NULL,
                    notification_type TEXT NOT NULL,
                    recipient TEXT NOT NULL,
                    status TEXT NOT NULL,
                    error_message TEXT,
                    sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (alert_id) REFERENCES alerts (id)
                )
            ''')

            # Alert escalation rules
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS escalation_rules (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    severity TEXT NOT NULL,
                    escalation_delay_minutes INTEGER NOT NULL,
                    escalation_emails TEXT NOT NULL,
                    escalation_message TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            conn.commit()

        self._insert_default_rules()
        logger.info("Alerts database initialized successfully")

    def _insert_default_rules(self):
        """Insert default alert and escalation rules"""
        default_escalation_rules = [
            ('HIGH', 30, '<EMAIL>', 'High priority alert requires attention', True),
            ('CRITICAL', 15, '<EMAIL>,<EMAIL>', 'Critical alert - immediate action required', True)
        ]

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            for rule in default_escalation_rules:
                cursor.execute('''
                    INSERT OR IGNORE INTO escalation_rules
                    (severity, escalation_delay_minutes, escalation_emails, escalation_message, is_active)
                    VALUES (?, ?, ?, ?, ?)
                ''', rule)

            conn.commit()

    def create_alert(self, device_id: str, alert_type: AlertType, severity: AlertSeverity,
                    message: str, current_value: float = None, threshold_value: float = None,
                    location: str = "", product_type: str = "", metadata: Dict = None) -> int:
        """Create a new alert"""
        try:
            # Check cooldown
            cooldown_key = f"{device_id}_{alert_type.value}"
            if self._is_in_cooldown(cooldown_key):
                logger.debug(f"Alert {cooldown_key} is in cooldown period")
                return None

            alert = Alert(
                device_id=device_id,
                alert_type=alert_type,
                severity=severity,
                message=message,
                current_value=current_value,
                threshold_value=threshold_value,
                location=location,
                product_type=product_type,
                created_at=datetime.now().isoformat(),
                metadata=metadata or {}
            )

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO alerts
                    (device_id, alert_type, severity, message, current_value, threshold_value,
                     location, product_type, metadata, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    alert.device_id, alert.alert_type.value, alert.severity.value,
                    alert.message, alert.current_value, alert.threshold_value,
                    alert.location, alert.product_type,
                    json.dumps(alert.metadata), alert.created_at
                ))

                alert_id = cursor.lastrowid
                conn.commit()

            # Set cooldown
            self._set_cooldown(cooldown_key)

            # Queue notification
            self._queue_notification(alert_id)

            logger.info(f"Created alert {alert_id}: {alert.severity.value} - {alert.message}")
            return alert_id

        except Exception as e:
            logger.error(f"Error creating alert: {e}")
            return None

    def get_alerts(self, acknowledged: bool = False, severity: str = None,
                  device_id: str = None, limit: int = 100) -> List[Dict]:
        """Get alerts with filtering options"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                query = "SELECT * FROM alerts WHERE is_acknowledged = ?"
                params = [acknowledged]

                if severity:
                    query += " AND severity = ?"
                    params.append(severity)

                if device_id:
                    query += " AND device_id = ?"
                    params.append(device_id)

                query += " ORDER BY created_at DESC LIMIT ?"
                params.append(limit)

                cursor.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"Error getting alerts: {e}")
            return []

    def acknowledge_alert(self, alert_id: int, acknowledged_by: str) -> bool:
        """Acknowledge an alert"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE alerts
                    SET is_acknowledged = 1, acknowledged_by = ?, acknowledged_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (acknowledged_by, alert_id))

                conn.commit()

                if cursor.rowcount > 0:
                    logger.info(f"Alert {alert_id} acknowledged by {acknowledged_by}")
                    return True
                else:
                    logger.warning(f"Alert {alert_id} not found for acknowledgment")
                    return False

        except Exception as e:
            logger.error(f"Error acknowledging alert {alert_id}: {e}")
            return False

    def _is_in_cooldown(self, cooldown_key: str) -> bool:
        """Check if alert is in cooldown period"""
        if cooldown_key in self._alert_cooldowns:
            cooldown_time = self._alert_cooldowns[cooldown_key]
            if datetime.now() < cooldown_time:
                return True
            else:
                del self._alert_cooldowns[cooldown_key]
        return False

    def _set_cooldown(self, cooldown_key: str, minutes: int = 15):
        """Set cooldown period for alert type"""
        cooldown_time = datetime.now() + timedelta(minutes=minutes)
        self._alert_cooldowns[cooldown_key] = cooldown_time

    def _queue_notification(self, alert_id: int):
        """Queue alert for notification"""
        self._notification_queue.append(alert_id)
        if not self._running:
            self.start_notification_service()

    def start_notification_service(self):
        """Start the notification service"""
        if self._running:
            return

        self._running = True
        self._notification_thread = threading.Thread(target=self._notification_worker, daemon=True)
        self._notification_thread.start()
        logger.info("Alert notification service started")

    def stop_notification_service(self):
        """Stop the notification service"""
        self._running = False
        if self._notification_thread:
            self._notification_thread.join(timeout=5)
        logger.info("Alert notification service stopped")

    def _notification_worker(self):
        """Background worker for processing notifications"""
        while self._running:
            try:
                if self._notification_queue:
                    alert_id = self._notification_queue.pop(0)
                    self._process_notification(alert_id)
                else:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"Error in notification worker: {e}")
                time.sleep(5)

    def _process_notification(self, alert_id: int):
        """Process a single alert notification"""
        try:
            # Get alert details
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM alerts WHERE id = ?', (alert_id,))
                alert = cursor.fetchone()

                if not alert:
                    logger.warning(f"Alert {alert_id} not found for notification")
                    return

                # Get system settings for email configuration
                cursor.execute('''
                    SELECT setting_key, setting_value FROM system_settings
                    WHERE setting_key IN ('alert_email', 'smtp_server', 'smtp_port',
                                         'smtp_username', 'smtp_password', 'enable_email_alerts')
                ''')
                settings = {row['setting_key']: row['setting_value'] for row in cursor.fetchall()}

                # Send email notification if enabled
                if settings.get('enable_email_alerts') == '1':
                    self._send_email_notification(alert, settings)

                # Mark notification as sent
                cursor.execute('''
                    UPDATE alerts SET notification_sent = 1 WHERE id = ?
                ''', (alert_id,))
                conn.commit()

        except Exception as e:
            logger.error(f"Error processing notification for alert {alert_id}: {e}")

    def _send_email_notification(self, alert: sqlite3.Row, settings: Dict):
        """Send email notification for alert"""
        try:
            # Prepare email content
            subject = f"[{alert['severity']}] Food Monitoring Alert - {alert['device_id']}"

            body = f"""
            Food Monitoring System Alert

            Device: {alert['device_id']}
            Location: {alert['location'] or 'Unknown'}
            Product Type: {alert['product_type'] or 'Unknown'}
            Alert Type: {alert['alert_type'].replace('_', ' ').title()}
            Severity: {alert['severity']}

            Message: {alert['message']}

            """

            if alert['current_value'] and alert['threshold_value']:
                body += f"Current Value: {alert['current_value']}\n"
                body += f"Threshold: {alert['threshold_value']}\n"

            body += f"\nTime: {alert['created_at']}\n"
            body += f"\nPlease check the monitoring dashboard for more details.\n"
            body += f"Dashboard: http://localhost:5000/alerts\n"

            # Send email
            self._send_email(
                to_email=settings.get('alert_email', '<EMAIL>'),
                subject=subject,
                body=body,
                smtp_settings=settings
            )

            # Log notification
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO alert_notifications
                    (alert_id, notification_type, recipient, status)
                    VALUES (?, ?, ?, ?)
                ''', (alert['id'], 'email', settings.get('alert_email'), 'sent'))
                conn.commit()

            logger.info(f"Email notification sent for alert {alert['id']}")

        except Exception as e:
            logger.error(f"Error sending email notification: {e}")

            # Log failed notification
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO alert_notifications
                    (alert_id, notification_type, recipient, status, error_message)
                    VALUES (?, ?, ?, ?, ?)
                ''', (alert['id'], 'email', settings.get('alert_email'), 'failed', str(e)))
                conn.commit()

    def _send_email(self, to_email: str, subject: str, body: str, smtp_settings: Dict):
        """Send email using SMTP settings"""
        try:
            msg = MIMEMultipart()
            msg['From'] = smtp_settings.get('smtp_username', '<EMAIL>')
            msg['To'] = to_email
            msg['Subject'] = subject

            msg.attach(MIMEText(body, 'plain'))

            # Connect to SMTP server
            server = smtplib.SMTP(
                smtp_settings.get('smtp_server', 'smtp.gmail.com'),
                int(smtp_settings.get('smtp_port', 587))
            )
            server.starttls()

            # Login if credentials provided
            username = smtp_settings.get('smtp_username')
            password = smtp_settings.get('smtp_password')
            if username and password:
                server.login(username, password)

            # Send email
            server.send_message(msg)
            server.quit()

        except Exception as e:
            logger.error(f"SMTP error: {e}")
            raise

    def get_alert_statistics(self, hours: int = 24) -> Dict:
        """Get alert statistics for the specified time period"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Total alerts
                cursor.execute('''
                    SELECT COUNT(*) FROM alerts
                    WHERE created_at >= datetime('now', '-{} hours')
                '''.format(hours))
                total_alerts = cursor.fetchone()[0]

                # Alerts by severity
                cursor.execute('''
                    SELECT severity, COUNT(*) FROM alerts
                    WHERE created_at >= datetime('now', '-{} hours')
                    GROUP BY severity
                '''.format(hours))
                severity_counts = dict(cursor.fetchall())

                # Alerts by type
                cursor.execute('''
                    SELECT alert_type, COUNT(*) FROM alerts
                    WHERE created_at >= datetime('now', '-{} hours')
                    GROUP BY alert_type
                '''.format(hours))
                type_counts = dict(cursor.fetchall())

                # Acknowledged vs unacknowledged
                cursor.execute('''
                    SELECT is_acknowledged, COUNT(*) FROM alerts
                    WHERE created_at >= datetime('now', '-{} hours')
                    GROUP BY is_acknowledged
                '''.format(hours))
                ack_counts = dict(cursor.fetchall())

                return {
                    'total_alerts': total_alerts,
                    'severity_counts': severity_counts,
                    'type_counts': type_counts,
                    'acknowledged': ack_counts.get(1, 0),
                    'unacknowledged': ack_counts.get(0, 0)
                }

        except Exception as e:
            logger.error(f"Error getting alert statistics: {e}")
            return {}

    def cleanup_old_alerts(self, days: int = 30) -> int:
        """Clean up old acknowledged alerts"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    DELETE FROM alerts
                    WHERE is_acknowledged = 1
                    AND acknowledged_at < datetime('now', '-{} days')
                '''.format(days))

                deleted_count = cursor.rowcount
                conn.commit()

                logger.info(f"Cleaned up {deleted_count} old alerts")
                return deleted_count

        except Exception as e:
            logger.error(f"Error cleaning up old alerts: {e}")
            return 0