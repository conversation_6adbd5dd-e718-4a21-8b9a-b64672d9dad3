# Food Monitoring System - Production Environment Configuration

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

FLASK_ENV=production
FLASK_DEBUG=false
SECRET_KEY=CHANGE-THIS-TO-A-SECURE-SECRET-KEY

# =============================================================================
# DATABASE SETTINGS
# =============================================================================

DATABASE_URL=postgresql://username:password@localhost/monitoring_prod

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

WTF_CSRF_ENABLED=true
SESSION_COOKIE_SECURE=true
JWT_SECRET_KEY=CHANGE-THIS-TO-A-SECURE-JWT-SECRET

# =============================================================================
# EMAIL SETTINGS
# =============================================================================

MAIL_SERVER=smtp.company.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=SECURE-EMAIL-PASSWORD
MAIL_DEFAULT_SENDER=<EMAIL>

DEFAULT_ALERT_RECIPIENTS=<EMAIL>,<EMAIL>

# =============================================================================
# MONITORING SETTINGS
# =============================================================================

SENSOR_DATA_RETENTION_DAYS=365
ALERT_RETENTION_DAYS=1095
HEALTH_CHECK_INTERVAL=300

DEFAULT_TEMP_MIN=-2.0
DEFAULT_TEMP_MAX=4.0
DEFAULT_HUMIDITY_MIN=80.0
DEFAULT_HUMIDITY_MAX=95.0

ALERT_ESCALATION_ENABLED=true
ALERT_COOLDOWN_MINUTES=15

# =============================================================================
# REDIS SETTINGS
# =============================================================================

REDIS_URL=redis://localhost:6379/0
CACHE_DEFAULT_TIMEOUT=600

# =============================================================================
# CELERY SETTINGS
# =============================================================================

CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# =============================================================================
# LOGGING SETTINGS
# =============================================================================

LOG_LEVEL=WARNING
LOG_FILE=logs/app_prod.log
LOG_MAX_BYTES=********
LOG_BACKUP_COUNT=10

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

SENTRY_DSN=https://<EMAIL>/project-id

# Webhook URLs for notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
TEAMS_WEBHOOK_URL=https://company.webhook.office.com/webhookb2/YOUR-TEAMS-WEBHOOK

# SMS service (Twilio)
TWILIO_ACCOUNT_SID=YOUR-TWILIO-ACCOUNT-SID
TWILIO_AUTH_TOKEN=YOUR-TWILIO-AUTH-TOKEN
TWILIO_PHONE_NUMBER=+**********

# =============================================================================
# API SETTINGS
# =============================================================================

API_RATE_LIMIT=1000 per hour

# =============================================================================
# PRODUCTION FEATURES
# =============================================================================

ENABLE_SENSOR_SIMULATION=false
ANALYTICS_ENABLED=true
