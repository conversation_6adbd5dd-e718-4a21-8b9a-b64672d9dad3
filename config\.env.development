# Food Monitoring System - Development Environment Configuration

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

FLASK_ENV=development
FLASK_DEBUG=true
SECRET_KEY=dev-secret-key-change-in-production

# =============================================================================
# DATABASE SETTINGS
# =============================================================================

DATABASE_URL=sqlite:///data/databases/monitoring_dev.db

# =============================================================================
# SECURITY SETTINGS (Relaxed for development)
# =============================================================================

WTF_CSRF_ENABLED=false
SESSION_COOKIE_SECURE=false

# =============================================================================
# EMAIL SETTINGS (Console backend for development)
# =============================================================================

MAIL_SERVER=localhost
MAIL_PORT=1025
MAIL_USE_TLS=false
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_DEFAULT_SENDER=<EMAIL>
MAIL_SUPPRESS_SEND=true

# =============================================================================
# MONITORING SETTINGS
# =============================================================================

SENSOR_DATA_RETENTION_DAYS=30
ALERT_RETENTION_DAYS=90
HEALTH_CHECK_INTERVAL=60

DEFAULT_TEMP_MIN=-2.0
DEFAULT_TEMP_MAX=4.0
DEFAULT_HUMIDITY_MIN=80.0
DEFAULT_HUMIDITY_MAX=95.0

ALERT_ESCALATION_ENABLED=true
ALERT_COOLDOWN_MINUTES=5

# =============================================================================
# REDIS SETTINGS
# =============================================================================

REDIS_URL=redis://localhost:6379/1
CACHE_DEFAULT_TIMEOUT=300

# =============================================================================
# LOGGING SETTINGS
# =============================================================================

LOG_LEVEL=DEBUG
LOG_FILE=logs/app_dev.log

# =============================================================================
# DEVELOPMENT FEATURES
# =============================================================================

ENABLE_SENSOR_SIMULATION=true
ENABLE_DEBUG_TOOLBAR=true
ENABLE_PROFILER=false
SEED_SAMPLE_DATA=true

# =============================================================================
# API SETTINGS
# =============================================================================

API_RATE_LIMIT=10000 per hour
