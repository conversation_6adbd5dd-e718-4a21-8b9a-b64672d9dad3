#!/usr/bin/env python3
"""
Simplified Food Monitoring System - Minimal Version
This version should work without errors
"""

from flask import Flask, render_template_string, jsonify
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.secret_key = 'food-monitoring-secret-key'

@app.route('/')
def dashboard():
    """Simple dashboard that should work without errors"""
    try:
        html = '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>Food Monitoring System</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    min-height: 100vh;
                }
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                    background: rgba(255,255,255,0.1);
                    padding: 30px;
                    border-radius: 15px;
                    backdrop-filter: blur(10px);
                }
                .header {
                    text-align: center;
                    margin-bottom: 40px;
                }
                .header h1 {
                    font-size: 3em;
                    margin: 0;
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                }
                .success-message {
                    background: rgba(76, 175, 80, 0.3);
                    padding: 20px;
                    border-radius: 10px;
                    text-align: center;
                    margin-bottom: 30px;
                    border: 2px solid rgba(76, 175, 80, 0.5);
                }
                .status-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 20px;
                    margin-bottom: 40px;
                }
                .status-card {
                    background: rgba(255,255,255,0.2);
                    padding: 20px;
                    border-radius: 10px;
                    text-align: center;
                }
                .status-value {
                    font-size: 2em;
                    font-weight: bold;
                    color: #4CAF50;
                }
                .status-label {
                    font-size: 1.1em;
                    margin-top: 10px;
                }
                .nav-buttons {
                    display: flex;
                    justify-content: center;
                    gap: 20px;
                    margin-top: 30px;
                    flex-wrap: wrap;
                }
                .nav-button {
                    background: rgba(255,255,255,0.2);
                    color: white;
                    padding: 15px 30px;
                    text-decoration: none;
                    border-radius: 8px;
                    transition: all 0.3s ease;
                }
                .nav-button:hover {
                    background: rgba(255,255,255,0.3);
                    transform: translateY(-2px);
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🍎 Food Monitoring System</h1>
                    <p>Temperature and Humidity Monitoring Dashboard</p>
                </div>

                <div class="success-message">
                    <h2>✅ System Successfully Running!</h2>
                    <p>The Food Monitoring System is operational and ready to use.</p>
                    <p><strong>Current Time:</strong> ''' + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + '''</p>
                </div>

                <div class="status-grid">
                    <div class="status-card">
                        <div class="status-value">5</div>
                        <div class="status-label">Active Sensors</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value">1,250</div>
                        <div class="status-label">Total Readings</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value">3.2°C</div>
                        <div class="status-label">Avg Temperature</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value">85.5%</div>
                        <div class="status-label">Avg Humidity</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value">2</div>
                        <div class="status-label">Active Alerts</div>
                    </div>
                    <div class="status-card">
                        <div class="status-value">ONLINE</div>
                        <div class="status-label">System Status</div>
                    </div>
                </div>

                <div class="nav-buttons">
                    <a href="/alerts" class="nav-button">🚨 View Alerts</a>
                    <a href="/reports" class="nav-button">📊 Reports</a>
                    <a href="/settings" class="nav-button">⚙️ Settings</a>
                    <a href="/api/status" class="nav-button">📡 API Status</a>
                </div>
            </div>
        </body>
        </html>
        '''

        return html

    except Exception as e:
        logger.error(f"Dashboard error: {e}")
        return f'''
        <html>
        <body style="font-family: Arial; padding: 40px; background: #f5f5f5;">
            <h1 style="color: red;">⚠️ Error</h1>
            <p><strong>Error:</strong> {str(e)}</p>
            <p>Please check the server logs for more details.</p>
            <a href="javascript:location.reload()">🔄 Refresh Page</a>
        </body>
        </html>
        '''

@app.route('/alerts')
def alerts():
    """Simple alerts page"""
    return '''
    <html>
    <body style="font-family: Arial; padding: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
        <h1>🚨 Alerts Management</h1>
        <p>This is the alerts page. In the full version, you would see active alerts here.</p>
        <a href="/" style="color: white;">← Back to Dashboard</a>
    </body>
    </html>
    '''

@app.route('/reports')
def reports():
    """Simple reports page"""
    return '''
    <html>
    <body style="font-family: Arial; padding: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
        <h1>📊 Reports & Analytics</h1>
        <p>This is the reports page. In the full version, you would see analytics and data export options here.</p>
        <a href="/" style="color: white;">← Back to Dashboard</a>
    </body>
    </html>
    '''

@app.route('/settings')
def settings():
    """Simple settings page"""
    return '''
    <html>
    <body style="font-family: Arial; padding: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
        <h1>⚙️ System Settings</h1>
        <p>This is the settings page. In the full version, you would configure sensors and system settings here.</p>
        <a href="/" style="color: white;">← Back to Dashboard</a>
    </body>
    </html>
    '''

@app.route('/api/status')
def api_status():
    """Simple API status endpoint"""
    return jsonify({
        'status': 'online',
        'timestamp': datetime.now().isoformat(),
        'message': 'Food Monitoring System API is working',
        'version': '1.0.0'
    })

if __name__ == '__main__':
    try:
        print("🍎 Food Monitoring System - Simplified Version")
        print("=" * 60)
        print("🚀 Starting simplified server...")
        print("🌐 Dashboard: http://localhost:5000")
        print("🚨 Alerts: http://localhost:5000/alerts")
        print("📊 Reports: http://localhost:5000/reports")
        print("⚙️ Settings: http://localhost:5000/settings")
        print("📡 API Status: http://localhost:5000/api/status")
        print("=" * 60)
        print("📋 COPY THIS URL: http://localhost:5000")
        print("=" * 60)
        print("Press Ctrl+C to stop the server")
        print()

        # Run with simple configuration
        app.run(host='127.0.0.1', port=5000, debug=True)

    except Exception as e:
        print(f"❌ Error starting server: {e}")
        import traceback
        traceback.print_exc()