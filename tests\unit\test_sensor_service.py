#!/usr/bin/env python3
"""
Unit Tests for Sensor Service
Tests the business logic for sensor data processing and management
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from app.core.services.sensor_service import SensorService, SensorReading, ThresholdViolation
from app.models.enums import AlertType, AlertSeverity
from tests.conftest import TestDataBuilder


class TestSensorService:
    """Test cases for SensorService"""
    
    @pytest.fixture
    def mock_sensor_repo(self):
        """Mock sensor repository"""
        return Mock()
    
    @pytest.fixture
    def mock_alert_repo(self):
        """Mock alert repository"""
        return Mock()
    
    @pytest.fixture
    def sensor_service(self, mock_sensor_repo, mock_alert_repo):
        """Create sensor service with mocked dependencies"""
        return SensorService(mock_sensor_repo, mock_alert_repo)
    
    def test_process_sensor_data_success(self, sensor_service, mock_sensor_repo, mock_alert_repo):
        """Test successful sensor data processing"""
        # Arrange
        sensor_data = TestDataBuilder.sensor_reading()
        mock_sensor_repo.store_reading.return_value = Mock(id=1)
        mock_sensor_repo.get_sensor_config.return_value = {
            'temp_min': -2.0,
            'temp_max': 4.0,
            'humidity_min': 80.0,
            'humidity_max': 95.0
        }
        
        # Act
        with patch('app.utils.validators.validate_sensor_data', return_value=sensor_data):
            result = sensor_service.process_sensor_data(sensor_data)
        
        # Assert
        assert result['status'] == 'success'
        assert result['reading_id'] == 1
        assert result['violations_detected'] == 0
        assert len(result['alerts_created']) == 0
        
        mock_sensor_repo.store_reading.assert_called_once()
        mock_sensor_repo.update_sensor_status.assert_called_once()
    
    def test_process_sensor_data_with_temperature_violation(self, sensor_service, mock_sensor_repo, mock_alert_repo):
        """Test sensor data processing with temperature threshold violation"""
        # Arrange
        sensor_data = TestDataBuilder.sensor_reading(temperature=6.0)  # Above threshold
        mock_sensor_repo.store_reading.return_value = Mock(id=1)
        mock_sensor_repo.get_sensor_config.return_value = {
            'temp_min': -2.0,
            'temp_max': 4.0,
            'humidity_min': 80.0,
            'humidity_max': 95.0
        }
        mock_alert_repo.create_alert.return_value = 123
        
        # Act
        with patch('app.utils.validators.validate_sensor_data', return_value=sensor_data):
            result = sensor_service.process_sensor_data(sensor_data)
        
        # Assert
        assert result['status'] == 'success'
        assert result['violations_detected'] == 1
        assert len(result['alerts_created']) == 1
        assert result['alerts_created'][0] == 123
        
        mock_alert_repo.create_alert.assert_called_once()
    
    def test_process_sensor_data_with_humidity_violation(self, sensor_service, mock_sensor_repo, mock_alert_repo):
        """Test sensor data processing with humidity threshold violation"""
        # Arrange
        sensor_data = TestDataBuilder.sensor_reading(humidity=70.0)  # Below threshold
        mock_sensor_repo.store_reading.return_value = Mock(id=1)
        mock_sensor_repo.get_sensor_config.return_value = {
            'temp_min': -2.0,
            'temp_max': 4.0,
            'humidity_min': 80.0,
            'humidity_max': 95.0
        }
        mock_alert_repo.create_alert.return_value = 124
        
        # Act
        with patch('app.utils.validators.validate_sensor_data', return_value=sensor_data):
            result = sensor_service.process_sensor_data(sensor_data)
        
        # Assert
        assert result['status'] == 'success'
        assert result['violations_detected'] == 1
        assert len(result['alerts_created']) == 1
        
        # Verify alert was created with correct parameters
        call_args = mock_alert_repo.create_alert.call_args[0][0]
        assert call_args['alert_type'] == AlertType.HUMIDITY_LOW
        assert call_args['severity'] == AlertSeverity.MEDIUM
        assert call_args['current_value'] == 70.0
        assert call_args['threshold_value'] == 80.0
    
    def test_process_sensor_data_invalid_data(self, sensor_service):
        """Test sensor data processing with invalid data"""
        # Arrange
        invalid_data = {'invalid': 'data'}
        
        # Act & Assert
        with patch('app.utils.validators.validate_sensor_data', side_effect=ValueError("Invalid data")):
            with pytest.raises(ValueError):
                sensor_service.process_sensor_data(invalid_data)
    
    def test_get_sensor_statistics(self, sensor_service, mock_sensor_repo):
        """Test getting sensor statistics"""
        # Arrange
        expected_stats = [
            {'device_id': 'SENSOR_001', 'avg_temp': 2.5, 'avg_humidity': 85.0},
            {'device_id': 'SENSOR_002', 'avg_temp': 1.5, 'avg_humidity': 80.0}
        ]
        mock_sensor_repo.get_statistics.return_value = expected_stats
        
        # Act
        result = sensor_service.get_sensor_statistics(hours=24)
        
        # Assert
        assert result == expected_stats
        mock_sensor_repo.get_statistics.assert_called_once()
        
        # Verify time range parameters
        call_args = mock_sensor_repo.get_statistics.call_args[0]
        assert call_args[0] is None  # device_id
        # Check that start_time and end_time are datetime objects
        assert isinstance(call_args[1], datetime)
        assert isinstance(call_args[2], datetime)
    
    def test_get_sensor_data(self, sensor_service, mock_sensor_repo):
        """Test getting sensor data for specific device"""
        # Arrange
        device_id = 'TEST_SENSOR_001'
        expected_data = [
            {'timestamp': datetime.now(), 'temperature': 2.5, 'humidity': 85.0},
            {'timestamp': datetime.now(), 'temperature': 2.3, 'humidity': 84.0}
        ]
        mock_sensor_repo.get_readings.return_value = expected_data
        
        # Act
        result = sensor_service.get_sensor_data(device_id, hours=12)
        
        # Assert
        assert result == expected_data
        mock_sensor_repo.get_readings.assert_called_once_with(
            device_id, 
            pytest.approx(datetime.now() - timedelta(hours=12), abs=timedelta(seconds=1)),
            pytest.approx(datetime.now(), abs=timedelta(seconds=1))
        )
    
    def test_add_sensor_config_success(self, sensor_service, mock_sensor_repo):
        """Test adding sensor configuration successfully"""
        # Arrange
        config = TestDataBuilder.sensor_config()
        mock_sensor_repo.add_sensor_config.return_value = True
        
        # Act
        with patch('app.models.schemas.SensorConfigSchema') as mock_schema:
            mock_schema.return_value.load.return_value = config
            result = sensor_service.add_sensor_config(config)
        
        # Assert
        assert result is True
        mock_sensor_repo.add_sensor_config.assert_called_once()
    
    def test_add_sensor_config_validation_error(self, sensor_service, mock_sensor_repo):
        """Test adding sensor configuration with validation error"""
        # Arrange
        invalid_config = {'invalid': 'config'}
        
        # Act
        with patch('app.models.schemas.SensorConfigSchema') as mock_schema:
            mock_schema.return_value.load.side_effect = ValueError("Validation error")
            result = sensor_service.add_sensor_config(invalid_config)
        
        # Assert
        assert result is False
        mock_sensor_repo.add_sensor_config.assert_not_called()
    
    def test_check_thresholds_no_violations(self, sensor_service, mock_sensor_repo):
        """Test threshold checking with no violations"""
        # Arrange
        reading = SensorReading(
            device_id='TEST_SENSOR',
            temperature=2.5,
            humidity=85.0,
            timestamp=datetime.now()
        )
        mock_sensor_repo.get_sensor_config.return_value = {
            'temp_min': -2.0,
            'temp_max': 4.0,
            'humidity_min': 80.0,
            'humidity_max': 95.0
        }
        
        # Act
        violations = sensor_service._check_thresholds(reading)
        
        # Assert
        assert len(violations) == 0
    
    def test_check_thresholds_temperature_high(self, sensor_service, mock_sensor_repo):
        """Test threshold checking with high temperature violation"""
        # Arrange
        reading = SensorReading(
            device_id='TEST_SENSOR',
            temperature=6.0,  # Above max
            humidity=85.0,
            timestamp=datetime.now()
        )
        mock_sensor_repo.get_sensor_config.return_value = {
            'temp_min': -2.0,
            'temp_max': 4.0,
            'humidity_min': 80.0,
            'humidity_max': 95.0
        }
        
        # Act
        violations = sensor_service._check_thresholds(reading)
        
        # Assert
        assert len(violations) == 1
        violation = violations[0]
        assert violation.violation_type == AlertType.TEMPERATURE_HIGH
        assert violation.severity == AlertSeverity.HIGH
        assert violation.current_value == 6.0
        assert violation.threshold_value == 4.0
    
    def test_check_thresholds_multiple_violations(self, sensor_service, mock_sensor_repo):
        """Test threshold checking with multiple violations"""
        # Arrange
        reading = SensorReading(
            device_id='TEST_SENSOR',
            temperature=-5.0,  # Below min
            humidity=70.0,     # Below min
            timestamp=datetime.now()
        )
        mock_sensor_repo.get_sensor_config.return_value = {
            'temp_min': -2.0,
            'temp_max': 4.0,
            'humidity_min': 80.0,
            'humidity_max': 95.0
        }
        
        # Act
        violations = sensor_service._check_thresholds(reading)
        
        # Assert
        assert len(violations) == 2
        
        # Check temperature violation
        temp_violation = next(v for v in violations if v.violation_type == AlertType.TEMPERATURE_LOW)
        assert temp_violation.severity == AlertSeverity.HIGH
        assert temp_violation.current_value == -5.0
        assert temp_violation.threshold_value == -2.0
        
        # Check humidity violation
        humidity_violation = next(v for v in violations if v.violation_type == AlertType.HUMIDITY_LOW)
        assert humidity_violation.severity == AlertSeverity.MEDIUM
        assert humidity_violation.current_value == 70.0
        assert humidity_violation.threshold_value == 80.0
    
    def test_check_thresholds_no_config(self, sensor_service, mock_sensor_repo):
        """Test threshold checking when no sensor config exists"""
        # Arrange
        reading = SensorReading(
            device_id='UNKNOWN_SENSOR',
            temperature=2.5,
            humidity=85.0,
            timestamp=datetime.now()
        )
        mock_sensor_repo.get_sensor_config.return_value = None
        
        # Act
        violations = sensor_service._check_thresholds(reading)
        
        # Assert
        assert len(violations) == 0
    
    def test_update_sensor_config_success(self, sensor_service, mock_sensor_repo):
        """Test updating sensor configuration successfully"""
        # Arrange
        device_id = 'TEST_SENSOR'
        config = TestDataBuilder.sensor_config()
        mock_sensor_repo.update_sensor_config.return_value = True
        
        # Act
        with patch('app.models.schemas.SensorConfigSchema') as mock_schema:
            mock_schema.return_value.load.return_value = config
            result = sensor_service.update_sensor_config(device_id, config)
        
        # Assert
        assert result is True
        mock_sensor_repo.update_sensor_config.assert_called_once_with(device_id, config)
    
    def test_get_all_sensors(self, sensor_service, mock_sensor_repo):
        """Test getting all sensors"""
        # Arrange
        expected_sensors = [
            TestDataBuilder.sensor_config(device_id='SENSOR_001'),
            TestDataBuilder.sensor_config(device_id='SENSOR_002')
        ]
        mock_sensor_repo.get_all_sensors.return_value = expected_sensors
        
        # Act
        result = sensor_service.get_all_sensors()
        
        # Assert
        assert result == expected_sensors
        mock_sensor_repo.get_all_sensors.assert_called_once()


class TestSensorReading:
    """Test cases for SensorReading data class"""
    
    def test_sensor_reading_creation(self):
        """Test creating a sensor reading"""
        # Arrange
        timestamp = datetime.now()
        
        # Act
        reading = SensorReading(
            device_id='TEST_SENSOR',
            temperature=2.5,
            humidity=85.0,
            timestamp=timestamp,
            location='Test Location',
            product_type='FRESH_MEAT'
        )
        
        # Assert
        assert reading.device_id == 'TEST_SENSOR'
        assert reading.temperature == 2.5
        assert reading.humidity == 85.0
        assert reading.timestamp == timestamp
        assert reading.location == 'Test Location'
        assert reading.product_type == 'FRESH_MEAT'
    
    def test_sensor_reading_optional_fields(self):
        """Test creating a sensor reading with optional fields"""
        # Arrange
        timestamp = datetime.now()
        
        # Act
        reading = SensorReading(
            device_id='TEST_SENSOR',
            temperature=2.5,
            humidity=85.0,
            timestamp=timestamp
        )
        
        # Assert
        assert reading.device_id == 'TEST_SENSOR'
        assert reading.temperature == 2.5
        assert reading.humidity == 85.0
        assert reading.timestamp == timestamp
        assert reading.location is None
        assert reading.product_type is None


class TestThresholdViolation:
    """Test cases for ThresholdViolation data class"""
    
    def test_threshold_violation_creation(self):
        """Test creating a threshold violation"""
        # Act
        violation = ThresholdViolation(
            device_id='TEST_SENSOR',
            violation_type=AlertType.TEMPERATURE_HIGH,
            current_value=6.0,
            threshold_value=4.0,
            severity=AlertSeverity.HIGH,
            message='Temperature too high'
        )
        
        # Assert
        assert violation.device_id == 'TEST_SENSOR'
        assert violation.violation_type == AlertType.TEMPERATURE_HIGH
        assert violation.current_value == 6.0
        assert violation.threshold_value == 4.0
        assert violation.severity == AlertSeverity.HIGH
        assert violation.message == 'Temperature too high'
