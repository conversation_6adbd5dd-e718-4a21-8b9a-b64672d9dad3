#!/usr/bin/env python3
"""
Settings Manager Backend
Handles all system settings, configurations, and preferences
"""

import sqlite3
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SystemSettings:
    """System settings data class"""
    company_name: str = "Food Processing Company"
    alert_email: str = "<EMAIL>"
    smtp_server: str = "smtp.gmail.com"
    smtp_port: int = 587
    smtp_username: str = ""
    smtp_password: str = ""
    check_interval: int = 60
    data_retention_days: int = 30
    dashboard_refresh: int = 30
    temperature_unit: str = "celsius"
    timezone: str = "UTC"
    max_alerts_per_hour: int = 10
    enable_email_alerts: bool = True
    enable_sms_alerts: bool = False
    backup_enabled: bool = True
    backup_interval_hours: int = 24

@dataclass
class SensorConfig:
    """Sensor configuration data class"""
    device_id: str
    location: str
    product_type: str
    temp_min: float
    temp_max: float
    humidity_min: float
    humidity_max: float
    alert_email: str = ""
    is_active: bool = True
    calibration_offset_temp: float = 0.0
    calibration_offset_humidity: float = 0.0
    alert_cooldown_minutes: int = 15
    created_at: str = ""
    updated_at: str = ""

class SettingsManager:
    """Comprehensive settings management system"""

    def __init__(self, db_path: str = "food_monitoring.db"):
        self.db_path = db_path
        self.init_database()
        self._cache = {}
        self._cache_timestamp = None

    def init_database(self):
        """Initialize settings database tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # System settings table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    setting_key TEXT UNIQUE NOT NULL,
                    setting_value TEXT NOT NULL,
                    setting_type TEXT DEFAULT 'string',
                    description TEXT,
                    category TEXT DEFAULT 'general',
                    is_encrypted BOOLEAN DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Sensor configurations table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sensor_configs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_id TEXT UNIQUE NOT NULL,
                    location TEXT NOT NULL,
                    product_type TEXT,
                    temp_min REAL NOT NULL,
                    temp_max REAL NOT NULL,
                    humidity_min REAL NOT NULL,
                    humidity_max REAL NOT NULL,
                    alert_email TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    calibration_offset_temp REAL DEFAULT 0.0,
                    calibration_offset_humidity REAL DEFAULT 0.0,
                    alert_cooldown_minutes INTEGER DEFAULT 15,
                    metadata TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Settings history table for audit trail
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS settings_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    setting_key TEXT NOT NULL,
                    old_value TEXT,
                    new_value TEXT,
                    changed_by TEXT,
                    change_reason TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # User preferences table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_preferences (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    preference_key TEXT NOT NULL,
                    preference_value TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(user_id, preference_key)
                )
            ''')

            conn.commit()

        self._insert_default_settings()
        logger.info("Settings database initialized successfully")

    def _insert_default_settings(self):
        """Insert default system settings"""
        default_settings = SystemSettings()
        settings_dict = asdict(default_settings)

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            for key, value in settings_dict.items():
                # Determine setting type
                setting_type = type(value).__name__
                if setting_type == 'bool':
                    setting_type = 'boolean'
                elif setting_type == 'int':
                    setting_type = 'integer'
                elif setting_type == 'float':
                    setting_type = 'float'
                else:
                    setting_type = 'string'

                # Convert value to string for storage
                if isinstance(value, bool):
                    value_str = '1' if value else '0'
                else:
                    value_str = str(value)

                cursor.execute('''
                    INSERT OR IGNORE INTO system_settings
                    (setting_key, setting_value, setting_type, description, category)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    key, value_str, setting_type,
                    self._get_setting_description(key),
                    self._get_setting_category(key)
                ))

            conn.commit()

    def _get_setting_description(self, key: str) -> str:
        """Get description for a setting key"""
        descriptions = {
            'company_name': 'Company name displayed in reports and emails',
            'alert_email': 'Default email address for system alerts',
            'smtp_server': 'SMTP server for sending email notifications',
            'smtp_port': 'SMTP server port number',
            'smtp_username': 'SMTP authentication username',
            'smtp_password': 'SMTP authentication password (encrypted)',
            'check_interval': 'Monitoring check interval in seconds',
            'data_retention_days': 'Number of days to retain sensor data',
            'dashboard_refresh': 'Dashboard auto-refresh interval in seconds',
            'temperature_unit': 'Temperature display unit (celsius/fahrenheit)',
            'timezone': 'System timezone for timestamps',
            'max_alerts_per_hour': 'Maximum alerts per hour to prevent spam',
            'enable_email_alerts': 'Enable email alert notifications',
            'enable_sms_alerts': 'Enable SMS alert notifications',
            'backup_enabled': 'Enable automatic database backups',
            'backup_interval_hours': 'Backup interval in hours'
        }
        return descriptions.get(key, f'Setting: {key}')

    def _get_setting_category(self, key: str) -> str:
        """Get category for a setting key"""
        categories = {
            'company_name': 'general',
            'alert_email': 'notifications',
            'smtp_server': 'email',
            'smtp_port': 'email',
            'smtp_username': 'email',
            'smtp_password': 'email',
            'check_interval': 'monitoring',
            'data_retention_days': 'data',
            'dashboard_refresh': 'interface',
            'temperature_unit': 'interface',
            'timezone': 'general',
            'max_alerts_per_hour': 'notifications',
            'enable_email_alerts': 'notifications',
            'enable_sms_alerts': 'notifications',
            'backup_enabled': 'backup',
            'backup_interval_hours': 'backup'
        }
        return categories.get(key, 'general')

    def get_setting(self, key: str, default: Any = None) -> Any:
        """Get a single setting value"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT setting_value, setting_type FROM system_settings
                    WHERE setting_key = ?
                ''', (key,))

                result = cursor.fetchone()
                if result:
                    value_str, setting_type = result
                    return self._convert_setting_value(value_str, setting_type)

                return default
        except Exception as e:
            logger.error(f"Error getting setting {key}: {e}")
            return default

    def get_all_settings(self) -> Dict[str, Any]:
        """Get all system settings"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT setting_key, setting_value, setting_type, description, category
                    FROM system_settings ORDER BY category, setting_key
                ''')

                settings = {}
                for row in cursor.fetchall():
                    key = row['setting_key']
                    value = self._convert_setting_value(row['setting_value'], row['setting_type'])
                    settings[key] = value

                return settings
        except Exception as e:
            logger.error(f"Error getting all settings: {e}")
            return {}

    def update_setting(self, key: str, value: Any, changed_by: str = "system",
                      change_reason: str = "") -> bool:
        """Update a single setting"""
        try:
            # Get current value for history
            old_value = self.get_setting(key)

            # Convert value to string for storage
            if isinstance(value, bool):
                value_str = '1' if value else '0'
                setting_type = 'boolean'
            elif isinstance(value, int):
                value_str = str(value)
                setting_type = 'integer'
            elif isinstance(value, float):
                value_str = str(value)
                setting_type = 'float'
            else:
                value_str = str(value)
                setting_type = 'string'

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Update setting
                cursor.execute('''
                    UPDATE system_settings
                    SET setting_value = ?, setting_type = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE setting_key = ?
                ''', (value_str, setting_type, key))

                # Add to history
                cursor.execute('''
                    INSERT INTO settings_history
                    (setting_key, old_value, new_value, changed_by, change_reason)
                    VALUES (?, ?, ?, ?, ?)
                ''', (key, str(old_value), value_str, changed_by, change_reason))

                conn.commit()

            # Clear cache
            self._cache.clear()
            logger.info(f"Setting {key} updated by {changed_by}")
            return True

        except Exception as e:
            logger.error(f"Error updating setting {key}: {e}")
            return False

    def _convert_setting_value(self, value_str: str, setting_type: str) -> Any:
        """Convert string value to appropriate type"""
        try:
            if setting_type == 'boolean':
                return value_str == '1'
            elif setting_type == 'integer':
                return int(value_str)
            elif setting_type == 'float':
                return float(value_str)
            else:
                return value_str
        except (ValueError, TypeError):
            return value_str

    # Sensor Configuration Methods
    def add_sensor_config(self, config: SensorConfig) -> bool:
        """Add a new sensor configuration"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Check if sensor already exists
                cursor.execute('SELECT device_id FROM sensor_configs WHERE device_id = ?',
                             (config.device_id,))
                if cursor.fetchone():
                    logger.warning(f"Sensor {config.device_id} already exists")
                    return False

                # Insert new sensor config
                cursor.execute('''
                    INSERT INTO sensor_configs
                    (device_id, location, product_type, temp_min, temp_max,
                     humidity_min, humidity_max, alert_email, is_active,
                     calibration_offset_temp, calibration_offset_humidity,
                     alert_cooldown_minutes, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    config.device_id, config.location, config.product_type,
                    config.temp_min, config.temp_max, config.humidity_min,
                    config.humidity_max, config.alert_email, config.is_active,
                    config.calibration_offset_temp, config.calibration_offset_humidity,
                    config.alert_cooldown_minutes, json.dumps({})
                ))

                conn.commit()
                logger.info(f"Added sensor configuration: {config.device_id}")
                return True

        except Exception as e:
            logger.error(f"Error adding sensor config {config.device_id}: {e}")
            return False

    def get_sensor_config(self, device_id: str = None) -> List[Dict] or Dict:
        """Get sensor configuration(s)"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                if device_id:
                    cursor.execute('SELECT * FROM sensor_configs WHERE device_id = ?', (device_id,))
                    result = cursor.fetchone()
                    return dict(result) if result else None
                else:
                    cursor.execute('SELECT * FROM sensor_configs ORDER BY device_id')
                    return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"Error getting sensor config: {e}")
            return [] if device_id is None else None

    def update_sensor_config(self, device_id: str, updates: Dict) -> bool:
        """Update sensor configuration"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Build update query dynamically
                set_clauses = []
                values = []

                for key, value in updates.items():
                    if key in ['temp_min', 'temp_max', 'humidity_min', 'humidity_max',
                              'calibration_offset_temp', 'calibration_offset_humidity',
                              'alert_cooldown_minutes', 'is_active']:
                        set_clauses.append(f"{key} = ?")
                        values.append(value)
                    elif key in ['location', 'product_type', 'alert_email']:
                        set_clauses.append(f"{key} = ?")
                        values.append(value)

                if not set_clauses:
                    return False

                set_clauses.append("updated_at = CURRENT_TIMESTAMP")
                values.append(device_id)

                query = f"UPDATE sensor_configs SET {', '.join(set_clauses)} WHERE device_id = ?"
                cursor.execute(query, values)

                conn.commit()
                logger.info(f"Updated sensor configuration: {device_id}")
                return True

        except Exception as e:
            logger.error(f"Error updating sensor config {device_id}: {e}")
            return False

    def delete_sensor_config(self, device_id: str) -> bool:
        """Delete sensor configuration"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM sensor_configs WHERE device_id = ?', (device_id,))
                conn.commit()

                if cursor.rowcount > 0:
                    logger.info(f"Deleted sensor configuration: {device_id}")
                    return True
                else:
                    logger.warning(f"Sensor {device_id} not found for deletion")
                    return False

        except Exception as e:
            logger.error(f"Error deleting sensor config {device_id}: {e}")
            return False

    def get_settings_by_category(self, category: str) -> Dict[str, Any]:
        """Get settings by category"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT setting_key, setting_value, setting_type
                    FROM system_settings WHERE category = ?
                    ORDER BY setting_key
                ''', (category,))

                settings = {}
                for row in cursor.fetchall():
                    key = row['setting_key']
                    value = self._convert_setting_value(row['setting_value'], row['setting_type'])
                    settings[key] = value

                return settings
        except Exception as e:
            logger.error(f"Error getting settings for category {category}: {e}")
            return {}

    def export_settings(self, file_path: str) -> bool:
        """Export all settings to JSON file"""
        try:
            settings = self.get_all_settings()
            sensor_configs = self.get_sensor_config()

            export_data = {
                'timestamp': datetime.now().isoformat(),
                'system_settings': settings,
                'sensor_configs': sensor_configs
            }

            with open(file_path, 'w') as f:
                json.dump(export_data, f, indent=2)

            logger.info(f"Settings exported to {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error exporting settings: {e}")
            return False

    def import_settings(self, file_path: str, changed_by: str = "import") -> bool:
        """Import settings from JSON file"""
        try:
            with open(file_path, 'r') as f:
                import_data = json.load(f)

            # Import system settings
            if 'system_settings' in import_data:
                for key, value in import_data['system_settings'].items():
                    self.update_setting(key, value, changed_by, "Imported from file")

            # Import sensor configs
            if 'sensor_configs' in import_data:
                for config_data in import_data['sensor_configs']:
                    config = SensorConfig(**config_data)
                    self.add_sensor_config(config)

            logger.info(f"Settings imported from {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error importing settings: {e}")
            return False