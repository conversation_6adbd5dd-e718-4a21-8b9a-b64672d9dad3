/* 
Food Monitoring System - Main Stylesheet
Modern, responsive design for the monitoring dashboard
*/

/* =============================================================================
   CSS CUSTOM PROPERTIES (VARIABLES)
   ============================================================================= */

:root {
  /* Color Palette */
  --primary-color: #667eea;
  --primary-dark: #5a67d8;
  --primary-light: #7c3aed;
  --secondary-color: #764ba2;
  --accent-color: #f093fb;
  
  /* Status Colors */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;
  
  /* Alert Severity Colors */
  --critical-color: #dc2626;
  --high-color: #ea580c;
  --medium-color: #d97706;
  --low-color: #65a30d;
  
  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* Typography */
  --font-family-sans: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  
  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  
  /* Border Radius */
  --radius-sm: 0.125rem;
  --radius: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* =============================================================================
   RESET AND BASE STYLES
   ============================================================================= */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* =============================================================================
   LAYOUT COMPONENTS
   ============================================================================= */

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.container-fluid {
  width: 100%;
  padding: 0 var(--space-4);
}

/* Header */
.header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  padding: var(--space-6) 0;
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.header-content {
  position: relative;
  z-index: 1;
}

.header h1 {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin-bottom: var(--space-2);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.header p {
  opacity: 0.9;
  font-size: var(--text-lg);
}

/* Navigation */
.nav {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: var(--space-3) 0;
  margin-top: var(--space-4);
  border-radius: var(--radius-lg);
}

.nav-list {
  display: flex;
  list-style: none;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.nav-link:hover,
.nav-link.active {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* Main Content */
.main {
  padding: var(--space-8) 0;
  min-height: calc(100vh - 200px);
}

/* =============================================================================
   CARD COMPONENTS
   ============================================================================= */

.card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow);
  padding: var(--space-6);
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card-header {
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--gray-200);
}

.card-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-2);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.card-subtitle {
  color: var(--gray-600);
  font-size: var(--text-sm);
}

.card-body {
  margin-bottom: var(--space-4);
}

.card-footer {
  padding-top: var(--space-4);
  border-top: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* =============================================================================
   GRID SYSTEMS
   ============================================================================= */

.grid {
  display: grid;
  gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* =============================================================================
   STATUS AND ALERT COMPONENTS
   ============================================================================= */

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

.status-item {
  text-align: center;
  padding: var(--space-6);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-fast);
}

.status-item:hover {
  background: white;
  box-shadow: var(--shadow-md);
}

.status-value {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--space-2);
  display: block;
}

.status-label {
  color: var(--gray-600);
  font-size: var(--text-sm);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Alert Styles */
.alert {
  padding: var(--space-4);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-4);
  border-left: 4px solid;
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
}

.alert-critical {
  background-color: #fef2f2;
  border-color: var(--critical-color);
  color: #991b1b;
}

.alert-high {
  background-color: #fff7ed;
  border-color: var(--high-color);
  color: #9a3412;
}

.alert-medium {
  background-color: #fffbeb;
  border-color: var(--medium-color);
  color: #92400e;
}

.alert-low {
  background-color: #f7fee7;
  border-color: var(--low-color);
  color: #365314;
}

/* =============================================================================
   RESPONSIVE DESIGN
   ============================================================================= */

@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-3);
  }
  
  .header h1 {
    font-size: var(--text-2xl);
  }
  
  .nav-list {
    flex-direction: column;
    gap: var(--space-1);
  }
  
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }
  
  .status-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-3);
  }
  
  .card {
    padding: var(--space-4);
  }
}

@media (max-width: 480px) {
  .header {
    padding: var(--space-4) 0;
  }
  
  .header h1 {
    font-size: var(--text-xl);
  }
  
  .main {
    padding: var(--space-4) 0;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
}
