#!/usr/bin/env python3
"""
Alert Service
Business logic for alert management and escalation
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from dataclasses import dataclass

from app.core.repositories.alert_repository import AlertRepository
from app.core.repositories.sensor_repository import SensorRepository
from app.models.enums import AlertType, AlertSeverity, AlertStatus
from app.utils.validators import validate_alert_data


@dataclass
class AlertEscalationRule:
    """Data class for alert escalation rules"""
    severity: AlertSeverity
    immediate_notify: bool
    escalate_after_minutes: int
    max_escalations: int
    notification_channels: List[str]


class AlertService:
    """
    Service class for alert management and escalation
    Handles business logic for alert operations
    """
    
    def __init__(self, alert_repository: AlertRepository, sensor_repository: SensorRepository):
        """
        Initialize alert service with dependencies
        
        Args:
            alert_repository: Repository for alert data access
            sensor_repository: Repository for sensor data access
        """
        self.alert_repo = alert_repository
        self.sensor_repo = sensor_repository
        self.logger = logging.getLogger(__name__)
        
        # Define escalation rules
        self.escalation_rules = {
            AlertSeverity.CRITICAL: AlertEscalationRule(
                severity=AlertSeverity.CRITICAL,
                immediate_notify=True,
                escalate_after_minutes=5,
                max_escalations=5,
                notification_channels=['email', 'sms', 'webhook']
            ),
            AlertSeverity.HIGH: AlertEscalationRule(
                severity=AlertSeverity.HIGH,
                immediate_notify=True,
                escalate_after_minutes=15,
                max_escalations=3,
                notification_channels=['email', 'webhook']
            ),
            AlertSeverity.MEDIUM: AlertEscalationRule(
                severity=AlertSeverity.MEDIUM,
                immediate_notify=True,
                escalate_after_minutes=30,
                max_escalations=2,
                notification_channels=['email']
            ),
            AlertSeverity.LOW: AlertEscalationRule(
                severity=AlertSeverity.LOW,
                immediate_notify=False,
                escalate_after_minutes=60,
                max_escalations=1,
                notification_channels=['email']
            )
        }
    
    def create_alert(self, alert_data: Dict[str, Any]) -> Optional[int]:
        """
        Create a new alert with validation and escalation logic
        
        Args:
            alert_data: Alert data dictionary
            
        Returns:
            Alert ID if created successfully, None otherwise
        """
        try:
            # Validate alert data
            validated_data = validate_alert_data(alert_data)
            
            # Check for duplicate alerts (cooldown period)
            if self._is_duplicate_alert(validated_data):
                self.logger.info(f"Duplicate alert suppressed for {validated_data['device_id']}")
                return None
            
            # Enrich alert data with sensor information
            enriched_data = self._enrich_alert_data(validated_data)
            
            # Create alert in repository
            alert_id = self.alert_repo.create_alert(enriched_data)
            
            if alert_id:
                # Apply escalation rules
                self._apply_escalation_rules(alert_id, enriched_data)
                
                self.logger.info(f"Alert {alert_id} created for device {validated_data['device_id']}")
            
            return alert_id
            
        except Exception as e:
            self.logger.error(f"Error creating alert: {e}")
            return None
    
    def get_alerts(self, 
                   device_id: Optional[str] = None,
                   status: Optional[AlertStatus] = None,
                   severity: Optional[AlertSeverity] = None,
                   hours: Optional[int] = None,
                   limit: int = 100) -> List[Dict]:
        """
        Get alerts with optional filtering
        
        Args:
            device_id: Filter by device ID
            status: Filter by alert status
            severity: Filter by severity level
            hours: Filter by time period in hours
            limit: Maximum number of alerts to return
            
        Returns:
            List of alerts matching the criteria
        """
        try:
            filters = {}
            
            if device_id:
                filters['device_id'] = device_id
            if status:
                filters['status'] = status
            if severity:
                filters['severity'] = severity
            if hours:
                filters['created_after'] = datetime.now() - timedelta(hours=hours)
            
            return self.alert_repo.get_alerts(filters, limit)
            
        except Exception as e:
            self.logger.error(f"Error getting alerts: {e}")
            return []
    
    def acknowledge_alert(self, alert_id: int, acknowledged_by: str, notes: Optional[str] = None) -> bool:
        """
        Acknowledge an alert
        
        Args:
            alert_id: Alert ID to acknowledge
            acknowledged_by: User who acknowledged the alert
            notes: Optional acknowledgment notes
            
        Returns:
            True if successful, False otherwise
        """
        try:
            acknowledgment_data = {
                'acknowledged_by': acknowledged_by,
                'acknowledged_at': datetime.now(),
                'notes': notes
            }
            
            success = self.alert_repo.acknowledge_alert(alert_id, acknowledgment_data)
            
            if success:
                self.logger.info(f"Alert {alert_id} acknowledged by {acknowledged_by}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error acknowledging alert {alert_id}: {e}")
            return False
    
    def resolve_alert(self, alert_id: int, resolved_by: str, resolution_notes: str) -> bool:
        """
        Resolve an alert
        
        Args:
            alert_id: Alert ID to resolve
            resolved_by: User who resolved the alert
            resolution_notes: Resolution notes
            
        Returns:
            True if successful, False otherwise
        """
        try:
            resolution_data = {
                'resolved_by': resolved_by,
                'resolved_at': datetime.now(),
                'resolution_notes': resolution_notes,
                'status': AlertStatus.RESOLVED
            }
            
            success = self.alert_repo.update_alert(alert_id, resolution_data)
            
            if success:
                self.logger.info(f"Alert {alert_id} resolved by {resolved_by}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error resolving alert {alert_id}: {e}")
            return False
    
    def get_alert_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get alert statistics for the specified time period
        
        Args:
            hours: Time period in hours
            
        Returns:
            Dictionary containing alert statistics
        """
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)
            
            return self.alert_repo.get_statistics(start_time, end_time)
            
        except Exception as e:
            self.logger.error(f"Error getting alert statistics: {e}")
            return {}
    
    def cleanup_old_alerts(self, days: int = 90) -> int:
        """
        Clean up old resolved alerts
        
        Args:
            days: Age threshold in days
            
        Returns:
            Number of alerts cleaned up
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            return self.alert_repo.cleanup_old_alerts(cutoff_date)
            
        except Exception as e:
            self.logger.error(f"Error cleaning up old alerts: {e}")
            return 0
    
    def process_escalations(self) -> int:
        """
        Process alert escalations for unacknowledged alerts
        
        Returns:
            Number of alerts escalated
        """
        try:
            escalated_count = 0
            
            # Get unacknowledged alerts that need escalation
            alerts_to_escalate = self.alert_repo.get_alerts_for_escalation()
            
            for alert in alerts_to_escalate:
                if self._should_escalate_alert(alert):
                    if self._escalate_alert(alert):
                        escalated_count += 1
            
            if escalated_count > 0:
                self.logger.info(f"Escalated {escalated_count} alerts")
            
            return escalated_count
            
        except Exception as e:
            self.logger.error(f"Error processing escalations: {e}")
            return 0
    
    def _is_duplicate_alert(self, alert_data: Dict[str, Any]) -> bool:
        """
        Check if this is a duplicate alert within the cooldown period
        
        Args:
            alert_data: Alert data to check
            
        Returns:
            True if duplicate, False otherwise
        """
        try:
            cooldown_minutes = 15  # Default cooldown period
            cutoff_time = datetime.now() - timedelta(minutes=cooldown_minutes)
            
            recent_alerts = self.alert_repo.get_recent_alerts(
                alert_data['device_id'],
                alert_data['alert_type'],
                cutoff_time
            )
            
            return len(recent_alerts) > 0
            
        except Exception as e:
            self.logger.error(f"Error checking for duplicate alerts: {e}")
            return False
    
    def _enrich_alert_data(self, alert_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enrich alert data with sensor configuration information
        
        Args:
            alert_data: Basic alert data
            
        Returns:
            Enriched alert data
        """
        try:
            # Get sensor configuration
            sensor_config = self.sensor_repo.get_sensor_config(alert_data['device_id'])
            
            if sensor_config:
                alert_data.update({
                    'location': sensor_config.get('location', ''),
                    'product_type': sensor_config.get('product_type', ''),
                    'alert_email': sensor_config.get('alert_email', '')
                })
            
            return alert_data
            
        except Exception as e:
            self.logger.error(f"Error enriching alert data: {e}")
            return alert_data
    
    def _apply_escalation_rules(self, alert_id: int, alert_data: Dict[str, Any]) -> None:
        """
        Apply escalation rules to a new alert
        
        Args:
            alert_id: Alert ID
            alert_data: Alert data
        """
        try:
            severity = alert_data.get('severity', AlertSeverity.MEDIUM)
            rule = self.escalation_rules.get(severity)
            
            if rule and rule.immediate_notify:
                # Schedule immediate notification
                self._schedule_notification(alert_id, alert_data, rule.notification_channels)
            
            # Schedule escalation if needed
            if rule and rule.max_escalations > 0:
                self._schedule_escalation(alert_id, rule)
                
        except Exception as e:
            self.logger.error(f"Error applying escalation rules: {e}")
    
    def _should_escalate_alert(self, alert: Dict[str, Any]) -> bool:
        """
        Check if an alert should be escalated
        
        Args:
            alert: Alert data
            
        Returns:
            True if should escalate, False otherwise
        """
        try:
            severity = alert.get('severity', AlertSeverity.MEDIUM)
            rule = self.escalation_rules.get(severity)
            
            if not rule:
                return False
            
            # Check if escalation time has passed
            created_at = alert.get('created_at')
            if not created_at:
                return False
            
            time_since_creation = datetime.now() - created_at
            escalation_threshold = timedelta(minutes=rule.escalate_after_minutes)
            
            # Check if alert is still unacknowledged and time threshold passed
            is_unacknowledged = alert.get('status') == AlertStatus.ACTIVE
            time_threshold_passed = time_since_creation >= escalation_threshold
            escalations_remaining = alert.get('escalation_count', 0) < rule.max_escalations
            
            return is_unacknowledged and time_threshold_passed and escalations_remaining
            
        except Exception as e:
            self.logger.error(f"Error checking escalation criteria: {e}")
            return False
    
    def _escalate_alert(self, alert: Dict[str, Any]) -> bool:
        """
        Escalate an alert
        
        Args:
            alert: Alert to escalate
            
        Returns:
            True if successful, False otherwise
        """
        try:
            alert_id = alert['id']
            current_escalations = alert.get('escalation_count', 0)
            
            # Update escalation count
            update_data = {
                'escalation_count': current_escalations + 1,
                'last_escalated_at': datetime.now()
            }
            
            success = self.alert_repo.update_alert(alert_id, update_data)
            
            if success:
                # Send escalation notification
                severity = alert.get('severity', AlertSeverity.MEDIUM)
                rule = self.escalation_rules.get(severity)
                if rule:
                    self._schedule_notification(alert_id, alert, rule.notification_channels, is_escalation=True)
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error escalating alert: {e}")
            return False
    
    def _schedule_notification(self, alert_id: int, alert_data: Dict[str, Any], 
                             channels: List[str], is_escalation: bool = False) -> None:
        """
        Schedule notification for an alert
        
        Args:
            alert_id: Alert ID
            alert_data: Alert data
            channels: Notification channels
            is_escalation: Whether this is an escalation notification
        """
        try:
            # This would typically queue a background task
            # For now, we'll just log the notification
            notification_type = "escalation" if is_escalation else "initial"
            self.logger.info(f"Scheduling {notification_type} notification for alert {alert_id} via {channels}")
            
        except Exception as e:
            self.logger.error(f"Error scheduling notification: {e}")
    
    def _schedule_escalation(self, alert_id: int, rule: AlertEscalationRule) -> None:
        """
        Schedule escalation for an alert
        
        Args:
            alert_id: Alert ID
            rule: Escalation rule
        """
        try:
            # This would typically schedule a background task
            # For now, we'll just log the escalation scheduling
            self.logger.info(f"Scheduling escalation for alert {alert_id} in {rule.escalate_after_minutes} minutes")
            
        except Exception as e:
            self.logger.error(f"Error scheduling escalation: {e}")
