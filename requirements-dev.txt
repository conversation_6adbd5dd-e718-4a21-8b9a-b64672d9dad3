# Food Monitoring System - Development Dependencies
# Additional packages for development and testing

# Include production requirements
-r requirements.txt

# =============================================================================
# Testing Framework
# =============================================================================
pytest==7.4.3
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-flask==1.3.0
pytest-asyncio==0.21.1
pytest-xdist==3.5.0
coverage==7.3.2

# =============================================================================
# Code Quality & Linting
# =============================================================================
flake8==6.1.0
black==23.11.0
isort==5.12.0
mypy==1.7.1
pylint==3.0.3
bandit==1.7.5

# =============================================================================
# Development Tools
# =============================================================================
Flask-DebugToolbar==0.13.1
Werkzeug==3.0.1
ipython==8.18.1
ipdb==0.13.13

# =============================================================================
# Documentation
# =============================================================================
Sphinx==7.2.6
sphinx-rtd-theme==1.3.0
sphinx-autodoc-typehints==1.25.2

# =============================================================================
# Database Tools
# =============================================================================
Flask-Migrate==4.0.5
factory-boy==3.3.0

# =============================================================================
# API Testing
# =============================================================================
httpx==0.25.2
responses==0.24.1

# =============================================================================
# Performance Profiling
# =============================================================================
py-spy==0.3.14
memory-profiler==0.61.0

# =============================================================================
# Environment Management
# =============================================================================
python-decouple==3.8
environs==10.0.0
