#!/usr/bin/env python3
"""
Food Monitoring System - Main Application
Temperature and Humidity Monitoring for Perishable Goods

This system monitors temperature and humidity conditions during transportation
and storage of perishable goods to ensure product quality and minimize spoilage.

Features:
- Real-time sensor data collection
- Threshold-based alerting
- Email notifications
- Web dashboard for monitoring
- Data analytics and reporting
- Configuration management
"""

import sys
import os
import argparse
import time
import signal
from datetime import datetime

# Add the monitoring_app directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import DatabaseManager
from monitoring_core import MonitoringCore
from sensor_simulator import MultiSensorSimulator
from app import app

class FoodMonitoringSystem:
    """Main application class for the Food Monitoring System"""

    def __init__(self):
        self.db = DatabaseManager()
        self.monitoring_core = MonitoringCore()
        self.simulator = None
        self.running = False

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\nReceived signal {signum}. Shutting down gracefully...")
        self.shutdown()
        sys.exit(0)

    def initialize_system(self):
        """Initialize the monitoring system with default configurations"""
        print("Initializing Food Monitoring System...")

        # Add default sensor configurations
        default_sensors = [
            {
                'device_id': 'SENSOR_001',
                'location': 'Cold Storage Room A',
                'product_type': 'Fresh Meat',
                'temp_min': -2.0,
                'temp_max': 4.0,
                'humidity_min': 80.0,
                'humidity_max': 95.0,
                'alert_email': '<EMAIL>',
                'is_active': True
            },
            {
                'device_id': 'SENSOR_002',
                'location': 'Dairy Refrigerator',
                'product_type': 'Dairy Products',
                'temp_min': 1.0,
                'temp_max': 4.0,
                'humidity_min': 75.0,
                'humidity_max': 85.0,
                'alert_email': '<EMAIL>',
                'is_active': True
            },
            {
                'device_id': 'SENSOR_003',
                'location': 'Vegetable Storage',
                'product_type': 'Fresh Vegetables',
                'temp_min': 0.0,
                'temp_max': 8.0,
                'humidity_min': 85.0,
                'humidity_max': 95.0,
                'alert_email': '<EMAIL>',
                'is_active': True
            },
            {
                'device_id': 'SENSOR_004',
                'location': 'Freezer Unit 1',
                'product_type': 'Frozen Foods',
                'temp_min': -25.0,
                'temp_max': -18.0,
                'humidity_min': 70.0,
                'humidity_max': 90.0,
                'alert_email': '<EMAIL>',
                'is_active': True
            },
            {
                'device_id': 'SENSOR_005',
                'location': 'Fruit Storage',
                'product_type': 'Fruits',
                'temp_min': 2.0,
                'temp_max': 10.0,
                'humidity_min': 80.0,
                'humidity_max': 90.0,
                'alert_email': '<EMAIL>',
                'is_active': True
            }
        ]

        for sensor_config in default_sensors:
            self.db.upsert_sensor_config(sensor_config)
            print(f"  ✓ Configured sensor {sensor_config['device_id']} at {sensor_config['location']}")

        print("System initialization complete!")

    def start_monitoring(self):
        """Start the monitoring system"""
        print("Starting monitoring system...")
        self.monitoring_core.start_monitoring()
        self.running = True
        print("  ✓ Monitoring system started")

    def start_simulation(self):
        """Start sensor simulation for testing"""
        print("Starting sensor simulation...")
        self.simulator = MultiSensorSimulator()

        # Add sensors matching the configured ones
        self.simulator.add_sensor('SENSOR_001', 'Cold Storage Room A', 'Fresh Meat')
        self.simulator.add_sensor('SENSOR_002', 'Dairy Refrigerator', 'Dairy Products')
        self.simulator.add_sensor('SENSOR_003', 'Vegetable Storage', 'Fresh Vegetables')
        self.simulator.add_sensor('SENSOR_004', 'Freezer Unit 1', 'Frozen Foods')
        self.simulator.add_sensor('SENSOR_005', 'Fruit Storage', 'Fruits')

        self.simulator.start_all(interval=30)
        print("  ✓ Sensor simulation started")

    def start_web_server(self, host='0.0.0.0', port=5000, debug=False):
        """Start the web dashboard"""
        print(f"Starting web dashboard on http://{host}:{port}")
        app.run(host=host, port=port, debug=debug, use_reloader=False)

    def shutdown(self):
        """Shutdown the system gracefully"""
        print("Shutting down Food Monitoring System...")

        if self.simulator:
            self.simulator.stop_all()
            print("  ✓ Sensor simulation stopped")

        if self.running:
            self.monitoring_core.stop_monitoring()
            print("  ✓ Monitoring system stopped")

        print("Shutdown complete!")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Food Monitoring System')
    parser.add_argument('--init', action='store_true', help='Initialize system with default configurations')
    parser.add_argument('--simulate', action='store_true', help='Start sensor simulation')
    parser.add_argument('--host', default='0.0.0.0', help='Web server host (default: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5000, help='Web server port (default: 5000)')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')

    args = parser.parse_args()

    # Create system instance
    system = FoodMonitoringSystem()

    try:
        # Initialize if requested
        if args.init:
            system.initialize_system()

        # Start monitoring
        system.start_monitoring()

        # Start simulation if requested
        if args.simulate:
            system.start_simulation()

        print("\n" + "="*60)
        print("🍎 FOOD MONITORING SYSTEM STARTED")
        print("="*60)
        print(f"📊 Web Dashboard: http://{args.host}:{args.port}")
        print(f"📡 API Endpoint: http://{args.host}:{args.port}/api/data")
        print("📧 Email alerts: Configure in settings")
        print("🔧 Sensor simulation:", "ENABLED" if args.simulate else "DISABLED")
        print("="*60)
        print("Press Ctrl+C to stop the system")
        print()

        # Start web server (this will block)
        system.start_web_server(args.host, args.port, args.debug)

    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
    finally:
        system.shutdown()

if __name__ == '__main__':
    main()