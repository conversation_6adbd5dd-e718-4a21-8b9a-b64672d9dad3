#!/usr/bin/env python3
"""
Sensor Service
Business logic for sensor data processing and management
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from dataclasses import dataclass

from app.core.repositories.sensor_repository import SensorRepository
from app.core.repositories.alert_repository import AlertRepository
from app.models.schemas import SensorDataSchema, SensorConfigSchema
from app.models.enums import AlertType, AlertSeverity
from app.utils.validators import validate_sensor_data


@dataclass
class SensorReading:
    """Data class for sensor readings"""
    device_id: str
    temperature: float
    humidity: float
    timestamp: datetime
    location: Optional[str] = None
    product_type: Optional[str] = None


@dataclass
class ThresholdViolation:
    """Data class for threshold violations"""
    device_id: str
    violation_type: AlertType
    current_value: float
    threshold_value: float
    severity: AlertSeverity
    message: str


class SensorService:
    """
    Service class for sensor data processing and management
    Handles business logic for sensor operations
    """
    
    def __init__(self, sensor_repository: SensorRepository, alert_repository: AlertRepository):
        """
        Initialize sensor service with dependencies
        
        Args:
            sensor_repository: Repository for sensor data access
            alert_repository: Repository for alert data access
        """
        self.sensor_repo = sensor_repository
        self.alert_repo = alert_repository
        self.logger = logging.getLogger(__name__)
    
    def process_sensor_data(self, sensor_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process incoming sensor data and check for threshold violations
        
        Args:
            sensor_data: Raw sensor data dictionary
            
        Returns:
            Dict containing processing results and any alerts generated
            
        Raises:
            ValueError: If sensor data is invalid
        """
        try:
            # Validate incoming data
            validated_data = validate_sensor_data(sensor_data)
            
            # Create sensor reading object
            reading = SensorReading(
                device_id=validated_data['device_id'],
                temperature=validated_data['temperature'],
                humidity=validated_data['humidity'],
                timestamp=datetime.now(),
                location=validated_data.get('location'),
                product_type=validated_data.get('product_type')
            )
            
            # Store sensor data
            stored_reading = self.sensor_repo.store_reading(reading)
            
            # Check for threshold violations
            violations = self._check_thresholds(reading)
            
            # Process any violations
            alerts_created = []
            for violation in violations:
                alert_id = self._create_threshold_alert(violation)
                if alert_id:
                    alerts_created.append(alert_id)
            
            # Update sensor status
            self._update_sensor_status(reading.device_id, reading.timestamp)
            
            result = {
                'status': 'success',
                'reading_id': stored_reading.id if stored_reading else None,
                'violations_detected': len(violations),
                'alerts_created': alerts_created,
                'timestamp': reading.timestamp.isoformat()
            }
            
            self.logger.info(f"Processed sensor data for {reading.device_id}: {len(violations)} violations")
            return result
            
        except Exception as e:
            self.logger.error(f"Error processing sensor data: {e}")
            raise
    
    def get_sensor_statistics(self, device_id: Optional[str] = None, hours: int = 24) -> List[Dict]:
        """
        Get sensor statistics for the specified time period
        
        Args:
            device_id: Specific sensor ID (None for all sensors)
            hours: Time period in hours
            
        Returns:
            List of sensor statistics
        """
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)
            
            return self.sensor_repo.get_statistics(device_id, start_time, end_time)
            
        except Exception as e:
            self.logger.error(f"Error getting sensor statistics: {e}")
            return []
    
    def get_sensor_data(self, device_id: str, hours: int = 24) -> List[Dict]:
        """
        Get historical sensor data for a specific device
        
        Args:
            device_id: Sensor device ID
            hours: Time period in hours
            
        Returns:
            List of sensor readings
        """
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)
            
            return self.sensor_repo.get_readings(device_id, start_time, end_time)
            
        except Exception as e:
            self.logger.error(f"Error getting sensor data for {device_id}: {e}")
            return []
    
    def get_all_sensors(self) -> List[Dict]:
        """
        Get all configured sensors
        
        Returns:
            List of sensor configurations
        """
        try:
            return self.sensor_repo.get_all_sensors()
        except Exception as e:
            self.logger.error(f"Error getting all sensors: {e}")
            return []
    
    def add_sensor_config(self, config: Dict[str, Any]) -> bool:
        """
        Add a new sensor configuration
        
        Args:
            config: Sensor configuration dictionary
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Validate configuration
            validated_config = SensorConfigSchema().load(config)
            
            # Store configuration
            return self.sensor_repo.add_sensor_config(validated_config)
            
        except Exception as e:
            self.logger.error(f"Error adding sensor config: {e}")
            return False
    
    def update_sensor_config(self, device_id: str, config: Dict[str, Any]) -> bool:
        """
        Update sensor configuration
        
        Args:
            device_id: Sensor device ID
            config: Updated configuration
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Validate configuration
            validated_config = SensorConfigSchema().load(config)
            
            # Update configuration
            return self.sensor_repo.update_sensor_config(device_id, validated_config)
            
        except Exception as e:
            self.logger.error(f"Error updating sensor config for {device_id}: {e}")
            return False
    
    def _check_thresholds(self, reading: SensorReading) -> List[ThresholdViolation]:
        """
        Check sensor reading against configured thresholds
        
        Args:
            reading: Sensor reading to check
            
        Returns:
            List of threshold violations
        """
        violations = []
        
        try:
            # Get sensor configuration
            config = self.sensor_repo.get_sensor_config(reading.device_id)
            if not config:
                self.logger.warning(f"No configuration found for sensor {reading.device_id}")
                return violations
            
            # Check temperature thresholds
            if reading.temperature < config['temp_min']:
                violations.append(ThresholdViolation(
                    device_id=reading.device_id,
                    violation_type=AlertType.TEMPERATURE_LOW,
                    current_value=reading.temperature,
                    threshold_value=config['temp_min'],
                    severity=AlertSeverity.HIGH,
                    message=f"Temperature {reading.temperature}°C below minimum {config['temp_min']}°C"
                ))
            elif reading.temperature > config['temp_max']:
                violations.append(ThresholdViolation(
                    device_id=reading.device_id,
                    violation_type=AlertType.TEMPERATURE_HIGH,
                    current_value=reading.temperature,
                    threshold_value=config['temp_max'],
                    severity=AlertSeverity.HIGH,
                    message=f"Temperature {reading.temperature}°C above maximum {config['temp_max']}°C"
                ))
            
            # Check humidity thresholds
            if reading.humidity < config['humidity_min']:
                violations.append(ThresholdViolation(
                    device_id=reading.device_id,
                    violation_type=AlertType.HUMIDITY_LOW,
                    current_value=reading.humidity,
                    threshold_value=config['humidity_min'],
                    severity=AlertSeverity.MEDIUM,
                    message=f"Humidity {reading.humidity}% below minimum {config['humidity_min']}%"
                ))
            elif reading.humidity > config['humidity_max']:
                violations.append(ThresholdViolation(
                    device_id=reading.device_id,
                    violation_type=AlertType.HUMIDITY_HIGH,
                    current_value=reading.humidity,
                    threshold_value=config['humidity_max'],
                    severity=AlertSeverity.MEDIUM,
                    message=f"Humidity {reading.humidity}% above maximum {config['humidity_max']}%"
                ))
            
        except Exception as e:
            self.logger.error(f"Error checking thresholds for {reading.device_id}: {e}")
        
        return violations
    
    def _create_threshold_alert(self, violation: ThresholdViolation) -> Optional[int]:
        """
        Create an alert for a threshold violation
        
        Args:
            violation: Threshold violation details
            
        Returns:
            Alert ID if created successfully, None otherwise
        """
        try:
            alert_data = {
                'device_id': violation.device_id,
                'alert_type': violation.violation_type,
                'severity': violation.severity,
                'message': violation.message,
                'current_value': violation.current_value,
                'threshold_value': violation.threshold_value,
                'created_at': datetime.now()
            }
            
            return self.alert_repo.create_alert(alert_data)
            
        except Exception as e:
            self.logger.error(f"Error creating threshold alert: {e}")
            return None
    
    def _update_sensor_status(self, device_id: str, last_seen: datetime) -> None:
        """
        Update sensor last seen timestamp and status
        
        Args:
            device_id: Sensor device ID
            last_seen: Last seen timestamp
        """
        try:
            self.sensor_repo.update_sensor_status(device_id, last_seen)
        except Exception as e:
            self.logger.error(f"Error updating sensor status for {device_id}: {e}")
