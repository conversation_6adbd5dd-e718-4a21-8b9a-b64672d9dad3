#!/usr/bin/env python3
"""
Test Suite for Food Monitoring System
Comprehensive testing infrastructure with unit, integration, and e2e tests
"""

import os
import sys
import pytest
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Test configuration
TEST_DATABASE_URL = "sqlite:///:memory:"
TEST_REDIS_URL = "redis://localhost:6379/15"

__version__ = "1.0.0"
__author__ = "Food Monitoring System Team"
