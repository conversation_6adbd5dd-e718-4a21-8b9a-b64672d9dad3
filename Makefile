# Food Monitoring System - Build Automation
# Makefile for common development and deployment tasks

.PHONY: help install install-dev setup clean test lint format docker-build docker-run deploy

# Default target
.DEFAULT_GOAL := help

# Variables
PYTHON := python3
PIP := pip3
VENV := venv
DOCKER_IMAGE := food-monitoring-system
DOCKER_TAG := latest

# Colors for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
NC := \033[0m # No Color

# =============================================================================
# Help
# =============================================================================

help: ## Show this help message
	@echo "$(BLUE)Food Monitoring System - Build Automation$(NC)"
	@echo "$(BLUE)===========================================$(NC)"
	@echo ""
	@echo "Available targets:"
	@echo ""
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""

# =============================================================================
# Environment Setup
# =============================================================================

install: ## Install production dependencies
	@echo "$(BLUE)Installing production dependencies...$(NC)"
	$(PIP) install -r requirements.txt
	@echo "$(GREEN)Production dependencies installed successfully!$(NC)"

install-dev: ## Install development dependencies
	@echo "$(BLUE)Installing development dependencies...$(NC)"
	$(PIP) install -r requirements-dev.txt
	@echo "$(GREEN)Development dependencies installed successfully!$(NC)"

setup: ## Setup development environment
	@echo "$(BLUE)Setting up development environment...$(NC)"
	$(PYTHON) -m venv $(VENV)
	./$(VENV)/bin/pip install --upgrade pip
	./$(VENV)/bin/pip install -r requirements-dev.txt
	@echo "$(GREEN)Development environment setup complete!$(NC)"
	@echo "$(YELLOW)Activate with: source $(VENV)/bin/activate$(NC)"

setup-dirs: ## Create necessary directories
	@echo "$(BLUE)Creating necessary directories...$(NC)"
	mkdir -p data/databases data/exports data/backups data/uploads logs
	@echo "$(GREEN)Directories created successfully!$(NC)"

init-db: ## Initialize database
	@echo "$(BLUE)Initializing database...$(NC)"
	$(PYTHON) scripts/init_db.py
	@echo "$(GREEN)Database initialized successfully!$(NC)"

seed-data: ## Seed database with sample data
	@echo "$(BLUE)Seeding database with sample data...$(NC)"
	$(PYTHON) scripts/seed_data.py
	@echo "$(GREEN)Sample data seeded successfully!$(NC)"

# =============================================================================
# Development
# =============================================================================

run: ## Run development server
	@echo "$(BLUE)Starting development server...$(NC)"
	$(PYTHON) run.py

run-debug: ## Run development server with debug mode
	@echo "$(BLUE)Starting development server in debug mode...$(NC)"
	$(PYTHON) run.py --debug

run-prod: ## Run production server
	@echo "$(BLUE)Starting production server...$(NC)"
	$(PYTHON) run.py --production

# =============================================================================
# Testing
# =============================================================================

test: ## Run all tests
	@echo "$(BLUE)Running all tests...$(NC)"
	pytest tests/ -v

test-unit: ## Run unit tests only
	@echo "$(BLUE)Running unit tests...$(NC)"
	pytest tests/unit/ -v

test-integration: ## Run integration tests only
	@echo "$(BLUE)Running integration tests...$(NC)"
	pytest tests/integration/ -v

test-e2e: ## Run end-to-end tests only
	@echo "$(BLUE)Running end-to-end tests...$(NC)"
	pytest tests/e2e/ -v

test-coverage: ## Run tests with coverage report
	@echo "$(BLUE)Running tests with coverage...$(NC)"
	pytest tests/ --cov=app --cov-report=html --cov-report=term-missing
	@echo "$(GREEN)Coverage report generated in htmlcov/$(NC)"

test-watch: ## Run tests in watch mode
	@echo "$(BLUE)Running tests in watch mode...$(NC)"
	pytest-watch tests/ -- -v

# =============================================================================
# Code Quality
# =============================================================================

lint: ## Run all linting tools
	@echo "$(BLUE)Running linting tools...$(NC)"
	flake8 app/ tests/
	pylint app/
	mypy app/
	bandit -r app/
	@echo "$(GREEN)Linting completed!$(NC)"

format: ## Format code with black and isort
	@echo "$(BLUE)Formatting code...$(NC)"
	black app/ tests/
	isort app/ tests/
	@echo "$(GREEN)Code formatted successfully!$(NC)"

format-check: ## Check code formatting without making changes
	@echo "$(BLUE)Checking code formatting...$(NC)"
	black --check app/ tests/
	isort --check-only app/ tests/

# =============================================================================
# Docker
# =============================================================================

docker-build: ## Build Docker image
	@echo "$(BLUE)Building Docker image...$(NC)"
	docker build -f docker/Dockerfile -t $(DOCKER_IMAGE):$(DOCKER_TAG) .
	@echo "$(GREEN)Docker image built successfully!$(NC)"

docker-run: ## Run Docker container
	@echo "$(BLUE)Running Docker container...$(NC)"
	docker run -p 8000:8000 --env-file config/.env.production $(DOCKER_IMAGE):$(DOCKER_TAG)

docker-compose-up: ## Start all services with docker-compose
	@echo "$(BLUE)Starting all services with docker-compose...$(NC)"
	cd docker && docker-compose up -d
	@echo "$(GREEN)All services started successfully!$(NC)"

docker-compose-down: ## Stop all services
	@echo "$(BLUE)Stopping all services...$(NC)"
	cd docker && docker-compose down
	@echo "$(GREEN)All services stopped!$(NC)"

docker-compose-logs: ## View docker-compose logs
	@echo "$(BLUE)Viewing docker-compose logs...$(NC)"
	cd docker && docker-compose logs -f

# =============================================================================
# Database Management
# =============================================================================

db-migrate: ## Run database migrations
	@echo "$(BLUE)Running database migrations...$(NC)"
	flask db upgrade
	@echo "$(GREEN)Database migrations completed!$(NC)"

db-backup: ## Create database backup
	@echo "$(BLUE)Creating database backup...$(NC)"
	$(PYTHON) scripts/backup_db.py
	@echo "$(GREEN)Database backup created!$(NC)"

db-restore: ## Restore database from backup (requires BACKUP_FILE variable)
	@echo "$(BLUE)Restoring database from backup...$(NC)"
	$(PYTHON) scripts/restore_db.py $(BACKUP_FILE)
	@echo "$(GREEN)Database restored successfully!$(NC)"

# =============================================================================
# Deployment
# =============================================================================

deploy-staging: ## Deploy to staging environment
	@echo "$(BLUE)Deploying to staging environment...$(NC)"
	$(PYTHON) scripts/deploy.py --env staging
	@echo "$(GREEN)Deployment to staging completed!$(NC)"

deploy-production: ## Deploy to production environment
	@echo "$(BLUE)Deploying to production environment...$(NC)"
	$(PYTHON) scripts/deploy.py --env production
	@echo "$(GREEN)Deployment to production completed!$(NC)"

# =============================================================================
# Maintenance
# =============================================================================

clean: ## Clean up temporary files and caches
	@echo "$(BLUE)Cleaning up temporary files...$(NC)"
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .pytest_cache/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf dist/
	rm -rf build/
	@echo "$(GREEN)Cleanup completed!$(NC)"

clean-docker: ## Clean up Docker images and containers
	@echo "$(BLUE)Cleaning up Docker resources...$(NC)"
	docker system prune -f
	docker image prune -f
	@echo "$(GREEN)Docker cleanup completed!$(NC)"

logs: ## View application logs
	@echo "$(BLUE)Viewing application logs...$(NC)"
	tail -f logs/app.log

health-check: ## Check application health
	@echo "$(BLUE)Checking application health...$(NC)"
	$(PYTHON) scripts/health_check.py

# =============================================================================
# Documentation
# =============================================================================

docs: ## Generate documentation
	@echo "$(BLUE)Generating documentation...$(NC)"
	cd docs && make html
	@echo "$(GREEN)Documentation generated in docs/_build/html/$(NC)"

docs-serve: ## Serve documentation locally
	@echo "$(BLUE)Serving documentation locally...$(NC)"
	cd docs/_build/html && python -m http.server 8080

# =============================================================================
# Security
# =============================================================================

security-check: ## Run security checks
	@echo "$(BLUE)Running security checks...$(NC)"
	bandit -r app/
	safety check
	@echo "$(GREEN)Security checks completed!$(NC)"

# =============================================================================
# Performance
# =============================================================================

profile: ## Profile application performance
	@echo "$(BLUE)Profiling application performance...$(NC)"
	$(PYTHON) scripts/profile_app.py

load-test: ## Run load tests
	@echo "$(BLUE)Running load tests...$(NC)"
	$(PYTHON) scripts/load_test.py

# =============================================================================
# Utilities
# =============================================================================

requirements-update: ## Update requirements files
	@echo "$(BLUE)Updating requirements files...$(NC)"
	pip-compile requirements.in
	pip-compile requirements-dev.in
	@echo "$(GREEN)Requirements files updated!$(NC)"

version: ## Show version information
	@echo "$(BLUE)Food Monitoring System Version Information$(NC)"
	@echo "$(BLUE)=========================================$(NC)"
	@$(PYTHON) -c "from app import __version__; print(f'Application Version: {__version__}')"
	@$(PYTHON) --version
	@docker --version 2>/dev/null || echo "Docker not installed"
