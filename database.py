import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import json

class DatabaseManager:
    """Database manager for the monitoring system"""

    def __init__(self, db_path: str = "monitoring_app/monitoring.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """Initialize the database with required tables"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Sensor data table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sensor_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_id TEXT NOT NULL,
                    temperature REAL NOT NULL,
                    humidity REAL NOT NULL,
                    location TEXT NOT NULL,
                    product_type TEXT,
                    batch_id TEXT,
                    timestamp DATETIME NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Sensor configuration table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sensor_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_id TEXT UNIQUE NOT NULL,
                    location TEXT NOT NULL,
                    product_type TEXT,
                    temp_min REAL NOT NULL,
                    temp_max REAL NOT NULL,
                    humidity_min REAL NOT NULL,
                    humidity_max REAL NOT NULL,
                    alert_email TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Alerts table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_id TEXT NOT NULL,
                    alert_type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    message TEXT NOT NULL,
                    current_value REAL,
                    threshold_value REAL,
                    is_acknowledged BOOLEAN DEFAULT 0,
                    acknowledged_by TEXT,
                    acknowledged_at DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Product types table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS product_types (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    description TEXT,
                    temp_min REAL NOT NULL,
                    temp_max REAL NOT NULL,
                    humidity_min REAL NOT NULL,
                    humidity_max REAL NOT NULL,
                    shelf_life_hours INTEGER,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # System logs table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    level TEXT NOT NULL,
                    message TEXT NOT NULL,
                    component TEXT,
                    details TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Create indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_sensor_data_device_timestamp ON sensor_data(device_id, timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_alerts_device_created ON alerts(device_id, created_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_sensor_data_timestamp ON sensor_data(timestamp)')

            conn.commit()

        self._insert_default_data()

    def _insert_default_data(self):
        """Insert default product types and configurations"""
        default_products = [
            {
                'name': 'Fresh Meat',
                'description': 'Fresh meat products requiring cold storage',
                'temp_min': -2.0,
                'temp_max': 4.0,
                'humidity_min': 80.0,
                'humidity_max': 95.0,
                'shelf_life_hours': 72
            },
            {
                'name': 'Dairy Products',
                'description': 'Milk, cheese, yogurt and other dairy items',
                'temp_min': 1.0,
                'temp_max': 4.0,
                'humidity_min': 75.0,
                'humidity_max': 85.0,
                'shelf_life_hours': 168
            },
            {
                'name': 'Fresh Vegetables',
                'description': 'Fresh vegetables and leafy greens',
                'temp_min': 0.0,
                'temp_max': 8.0,
                'humidity_min': 85.0,
                'humidity_max': 95.0,
                'shelf_life_hours': 120
            },
            {
                'name': 'Frozen Foods',
                'description': 'Frozen food products',
                'temp_min': -25.0,
                'temp_max': -18.0,
                'humidity_min': 70.0,
                'humidity_max': 90.0,
                'shelf_life_hours': 8760
            },
            {
                'name': 'Fruits',
                'description': 'Fresh fruits requiring controlled atmosphere',
                'temp_min': 2.0,
                'temp_max': 10.0,
                'humidity_min': 80.0,
                'humidity_max': 90.0,
                'shelf_life_hours': 240
            }
        ]

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            for product in default_products:
                cursor.execute('''
                    INSERT OR IGNORE INTO product_types
                    (name, description, temp_min, temp_max, humidity_min, humidity_max, shelf_life_hours)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    product['name'], product['description'], product['temp_min'],
                    product['temp_max'], product['humidity_min'], product['humidity_max'],
                    product['shelf_life_hours']
                ))
            conn.commit()

    def insert_sensor_data(self, data: Dict) -> bool:
        """Insert sensor reading data"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO sensor_data
                    (device_id, temperature, humidity, location, product_type, batch_id, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    data['device_id'],
                    data['temperature'],
                    data['humidity'],
                    data['location'],
                    data.get('product_type'),
                    data.get('batch_id'),
                    data['timestamp']
                ))
                conn.commit()
                return True
        except Exception as e:
            self.log_error(f"Failed to insert sensor data: {str(e)}")
            return False

    def get_sensor_data(self, device_id: str = None, hours: int = 24) -> List[Dict]:
        """Get sensor data for the last N hours"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            if device_id:
                cursor.execute('''
                    SELECT * FROM sensor_data
                    WHERE device_id = ? AND timestamp >= datetime('now', '-{} hours')
                    ORDER BY timestamp DESC
                '''.format(hours), (device_id,))
            else:
                cursor.execute('''
                    SELECT * FROM sensor_data
                    WHERE timestamp >= datetime('now', '-{} hours')
                    ORDER BY timestamp DESC
                '''.format(hours))

            return [dict(row) for row in cursor.fetchall()]

    def insert_alert(self, device_id: str, alert_type: str, severity: str,
                    message: str, current_value: float = None,
                    threshold_value: float = None) -> bool:
        """Insert a new alert"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO alerts
                    (device_id, alert_type, severity, message, current_value, threshold_value)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (device_id, alert_type, severity, message, current_value, threshold_value))
                conn.commit()
                return True
        except Exception as e:
            self.log_error(f"Failed to insert alert: {str(e)}")
            return False

    def get_active_alerts(self) -> List[Dict]:
        """Get all unacknowledged alerts"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM alerts
                WHERE is_acknowledged = 0
                ORDER BY created_at DESC
            ''')
            return [dict(row) for row in cursor.fetchall()]

    def acknowledge_alert(self, alert_id: int, acknowledged_by: str) -> bool:
        """Acknowledge an alert"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE alerts
                    SET is_acknowledged = 1, acknowledged_by = ?, acknowledged_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (acknowledged_by, alert_id))
                conn.commit()
                return True
        except Exception as e:
            self.log_error(f"Failed to acknowledge alert: {str(e)}")
            return False

    def get_sensor_config(self, device_id: str = None) -> List[Dict]:
        """Get sensor configuration"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            if device_id:
                cursor.execute('SELECT * FROM sensor_config WHERE device_id = ?', (device_id,))
                result = cursor.fetchone()
                return dict(result) if result else None
            else:
                cursor.execute('SELECT * FROM sensor_config WHERE is_active = 1')
                return [dict(row) for row in cursor.fetchall()]

    def upsert_sensor_config(self, config: Dict) -> bool:
        """Insert or update sensor configuration"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO sensor_config
                    (device_id, location, product_type, temp_min, temp_max,
                     humidity_min, humidity_max, alert_email, is_active, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (
                    config['device_id'], config['location'], config.get('product_type'),
                    config['temp_min'], config['temp_max'], config['humidity_min'],
                    config['humidity_max'], config.get('alert_email'),
                    config.get('is_active', True)
                ))
                conn.commit()
                return True
        except Exception as e:
            self.log_error(f"Failed to upsert sensor config: {str(e)}")
            return False

    def get_product_types(self) -> List[Dict]:
        """Get all product types"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM product_types ORDER BY name')
            return [dict(row) for row in cursor.fetchall()]

    def log_error(self, message: str, component: str = "database", details: str = None):
        """Log system errors"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO system_logs (level, message, component, details)
                    VALUES (?, ?, ?, ?)
                ''', ("ERROR", message, component, details))
                conn.commit()
        except:
            pass  # Avoid infinite recursion if logging fails

    def get_statistics(self, device_id: str = None, hours: int = 24) -> Dict:
        """Get statistical summary of sensor data"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            base_query = '''
                SELECT
                    COUNT(*) as total_readings,
                    AVG(temperature) as avg_temp,
                    MIN(temperature) as min_temp,
                    MAX(temperature) as max_temp,
                    AVG(humidity) as avg_humidity,
                    MIN(humidity) as min_humidity,
                    MAX(humidity) as max_humidity
                FROM sensor_data
                WHERE timestamp >= datetime('now', '-{} hours')
            '''.format(hours)

            if device_id:
                base_query += ' AND device_id = ?'
                cursor.execute(base_query, (device_id,))
            else:
                cursor.execute(base_query)

            result = cursor.fetchone()
            if result:
                return {
                    'total_readings': result[0],
                    'avg_temp': round(result[1], 2) if result[1] else 0,
                    'min_temp': result[2],
                    'max_temp': result[3],
                    'avg_humidity': round(result[4], 2) if result[4] else 0,
                    'min_humidity': result[5],
                    'max_humidity': result[6]
                }
            return {}