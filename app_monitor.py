#!/usr/bin/env python3
"""
Food Monitoring System - Clean Application
Fixed frontend-backend integration with proper error handling
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, Response
from datetime import datetime, timedelta
import json
import os
import sys
import logging
import sqlite3

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.secret_key = 'food-monitoring-secret-key-change-this'

# Initialize backend system
backend = None
try:
    from backend import BackendManager, AlertType, AlertSeverity
    backend = BackendManager("food_monitoring.db")
    print("✅ Backend system initialized successfully")
    print("   • Settings Manager - System and sensor configuration")
    print("   • Alerts Manager - Real-time alerting and notifications")
    print("   • Reports Manager - Analytics and data export")
except Exception as e:
    print(f"❌ Error initializing backend: {e}")

# Fallback components for compatibility
db = None
monitoring_core = None
alert_system = None

try:
    from database import DatabaseManager
    from monitoring_core import MonitoringCore
    from alert_system import AlertSystem

    db = DatabaseManager()
    monitoring_core = MonitoringCore()
    alert_system = AlertSystem(monitoring_core.config)
    print("✅ Legacy components initialized successfully")
except Exception as e:
    print(f"⚠️ Legacy components not available: {e}")

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def get_safe_data(func, default=None):
    """Safely execute a function and return default on error"""
    try:
        return func()
    except Exception as e:
        logger.error(f"Error in {func.__name__}: {e}")
        return default

def ensure_dict(data, keys_with_defaults):
    """Ensure data is a dict with required keys"""
    if not isinstance(data, dict):
        data = {}
    
    for key, default_value in keys_with_defaults.items():
        if key not in data:
            data[key] = default_value
    
    return data

# ============================================================================
# DASHBOARD ROUTES
# ============================================================================

@app.route('/')
def dashboard():
    """Enhanced dashboard with proper error handling"""
    try:
        # Initialize default data structure
        overview = {
            'total_sensors': 0,
            'total_readings': 0,
            'total_alerts': 0,
            'avg_temperature': 0.0,
            'avg_humidity': 0.0,
            'monitoring_active': False,
            'active_alerts': 0
        }
        
        status = overview.copy()  # Status is same as overview for compatibility
        stats = overview.copy()   # Stats is same as overview for compatibility
        
        active_alerts = []
        sensor_stats = []
        system_health = {'health_status': 'Unknown', 'health_score': 0}

        if backend:
            # Get data from backend with error handling
            try:
                dashboard_data = get_safe_data(lambda: backend.get_dashboard_data(), {})
                if dashboard_data and 'overview' in dashboard_data:
                    overview.update(dashboard_data['overview'])
                    status.update(dashboard_data['overview'])
                    stats.update(dashboard_data['overview'])
            except Exception as e:
                logger.error(f"Error getting dashboard data: {e}")

            try:
                system_health = get_safe_data(lambda: backend.get_system_health(), system_health)
            except Exception as e:
                logger.error(f"Error getting system health: {e}")

            try:
                active_alerts = get_safe_data(lambda: backend.alerts.get_alerts(acknowledged=False, limit=10), [])
                # Update active alerts count
                overview['active_alerts'] = len(active_alerts)
                status['active_alerts'] = len(active_alerts)
                stats['active_alerts'] = len(active_alerts)
            except Exception as e:
                logger.error(f"Error getting alerts: {e}")

            try:
                sensor_stats = get_safe_data(lambda: backend.reports.get_sensor_statistics(hours=24), [])
                # Update sensor count
                overview['total_sensors'] = len(sensor_stats)
                status['total_sensors'] = len(sensor_stats)
                stats['total_sensors'] = len(sensor_stats)
            except Exception as e:
                logger.error(f"Error getting sensor statistics: {e}")

            # Set monitoring as active if backend is working
            overview['monitoring_active'] = True
            status['monitoring_active'] = True
            stats['monitoring_active'] = True

        elif db and monitoring_core:
            # Fallback to legacy system
            try:
                legacy_status = get_safe_data(lambda: monitoring_core.get_system_status(), {})
                if legacy_status:
                    overview.update(legacy_status)
                    status.update(legacy_status)
                    stats.update(legacy_status)
                
                active_alerts = get_safe_data(lambda: db.get_active_alerts(), [])
                legacy_stats = get_safe_data(lambda: db.get_statistics(hours=24), {})
                if legacy_stats:
                    stats.update(legacy_stats)
                
                system_health = {'health_status': 'Legacy Mode', 'health_score': 50}
                
                # Update counts
                overview['active_alerts'] = len(active_alerts)
                status['active_alerts'] = len(active_alerts)
                stats['active_alerts'] = len(active_alerts)
                
            except Exception as e:
                logger.error(f"Error with legacy system: {e}")
        else:
            # Demo fallback data
            overview.update({
                'total_sensors': 5,
                'total_readings': 1250,
                'total_alerts': 3,
                'avg_temperature': 3.2,
                'avg_humidity': 85.5,
                'monitoring_active': False,
                'active_alerts': 3
            })
            status.update(overview)
            stats.update(overview)

            active_alerts = [
                {
                    'id': 1,
                    'device_id': 'SENSOR_001',
                    'alert_type': 'TEMPERATURE_HIGH',
                    'severity': 'HIGH',
                    'message': 'Temperature exceeded safe limits',
                    'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'current_value': 6.5,
                    'threshold_value': 4.0
                }
            ]
            
            system_health = {'health_status': 'Demo Mode', 'health_score': 75}

        # Ensure all required template variables are present
        template_vars = {
            'overview': overview,
            'status': status,
            'stats': stats,
            'alerts': active_alerts,
            'sensor_statistics': sensor_stats,
            'system_health': system_health,
            'current_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'active_alerts_count': len(active_alerts)
        }

        return render_template('dashboard.html', **template_vars)

    except Exception as e:
        logger.error(f"Dashboard error: {e}")
        flash(f"Dashboard error: {e}", 'error')
        
        # Return safe fallback data
        safe_data = {
            'overview': {'total_sensors': 0, 'total_readings': 0, 'total_alerts': 0, 'avg_temperature': 0, 'avg_humidity': 0, 'monitoring_active': False, 'active_alerts': 0},
            'status': {'total_sensors': 0, 'total_readings': 0, 'total_alerts': 0, 'avg_temperature': 0, 'avg_humidity': 0, 'monitoring_active': False, 'active_alerts': 0},
            'stats': {'total_sensors': 0, 'total_readings': 0, 'total_alerts': 0, 'avg_temperature': 0, 'avg_humidity': 0, 'monitoring_active': False, 'active_alerts': 0},
            'alerts': [],
            'sensor_statistics': [],
            'system_health': {'health_status': 'Error', 'health_score': 0},
            'current_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'active_alerts_count': 0
        }
        
        return render_template('dashboard.html', **safe_data)

@app.route('/api/dashboard/data')
def dashboard_api():
    """API endpoint for dashboard data"""
    try:
        if backend:
            data = get_safe_data(lambda: backend.get_dashboard_data(), {})
            return jsonify(data)
        else:
            return jsonify({'error': 'Backend not available'}), 503
    except Exception as e:
        logger.error(f"Dashboard API error: {e}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# ALERTS ROUTES
# ============================================================================

@app.route('/alerts')
def alerts_page():
    """Enhanced alerts page with backend data"""
    try:
        active_alerts = []
        alert_stats = {}
        
        if backend:
            try:
                active_alerts = get_safe_data(lambda: backend.alerts.get_alerts(acknowledged=False, limit=100), [])
                alert_stats = get_safe_data(lambda: backend.alerts.get_alert_statistics(hours=24), {})
            except Exception as e:
                logger.error(f"Error getting alerts: {e}")
        else:
            # Demo fallback data
            active_alerts = [
                {
                    'id': 1,
                    'device_id': 'SENSOR_001',
                    'alert_type': 'TEMPERATURE_HIGH',
                    'severity': 'HIGH',
                    'message': 'Temperature exceeded safe limits',
                    'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'current_value': 6.5,
                    'threshold_value': 4.0
                }
            ]
            alert_stats = {'total_alerts_24h': 1, 'critical_alerts': 0, 'high_alerts': 1}

        return render_template('alerts.html', 
                             alerts=active_alerts, 
                             alert_statistics=alert_stats)

    except Exception as e:
        logger.error(f"Alerts page error: {e}")
        flash(f"Error loading alerts: {e}", 'error')
        return render_template('alerts.html', alerts=[], alert_statistics={})

# ============================================================================
# SENSOR DATA API
# ============================================================================

@app.route('/api/data', methods=['POST'])
def receive_sensor_data():
    """Enhanced API endpoint to receive sensor data"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Validate required fields
        required_fields = ['device_id', 'temperature', 'humidity']
        if not all(field in data for field in required_fields):
            return jsonify({'error': 'Missing required fields'}), 400

        if backend:
            # Process with backend
            try:
                sensor_config = backend.settings.get_sensor_config(data['device_id'])
                if sensor_config:
                    temp = data['temperature']
                    humidity = data['humidity']

                    # Check temperature thresholds
                    if temp < sensor_config['temp_min']:
                        backend.create_sensor_alert(
                            device_id=data['device_id'],
                            alert_type='TEMPERATURE_LOW',
                            severity='HIGH',
                            message=f"Temperature {temp}°C is below minimum {sensor_config['temp_min']}°C",
                            current_value=temp,
                            threshold_value=sensor_config['temp_min']
                        )
                    elif temp > sensor_config['temp_max']:
                        backend.create_sensor_alert(
                            device_id=data['device_id'],
                            alert_type='TEMPERATURE_HIGH',
                            severity='HIGH',
                            message=f"Temperature {temp}°C is above maximum {sensor_config['temp_max']}°C",
                            current_value=temp,
                            threshold_value=sensor_config['temp_max']
                        )

                    # Check humidity thresholds
                    if humidity < sensor_config['humidity_min']:
                        backend.create_sensor_alert(
                            device_id=data['device_id'],
                            alert_type='HUMIDITY_LOW',
                            severity='MEDIUM',
                            message=f"Humidity {humidity}% is below minimum {sensor_config['humidity_min']}%",
                            current_value=humidity,
                            threshold_value=sensor_config['humidity_min']
                        )
                    elif humidity > sensor_config['humidity_max']:
                        backend.create_sensor_alert(
                            device_id=data['device_id'],
                            alert_type='HUMIDITY_HIGH',
                            severity='MEDIUM',
                            message=f"Humidity {humidity}% is above maximum {sensor_config['humidity_max']}%",
                            current_value=humidity,
                            threshold_value=sensor_config['humidity_max']
                        )

                return jsonify({'status': 'success', 'message': 'Data received and processed'}), 200
            
            except Exception as e:
                logger.error(f"Backend processing error: {e}")
                return jsonify({'status': 'success', 'message': 'Data received (processing error)'}), 200

        elif monitoring_core:
            # Fallback to legacy processing
            try:
                success = monitoring_core.process_sensor_data(data)
                if success:
                    return jsonify({'status': 'success', 'message': 'Data received'}), 200
                else:
                    return jsonify({'error': 'Failed to process data'}), 500
            except Exception as e:
                logger.error(f"Legacy processing error: {e}")
                return jsonify({'status': 'success', 'message': 'Data received (legacy error)'}), 200
        else:
            # No processing system available, just acknowledge
            return jsonify({'status': 'success', 'message': 'Data received (no processing)'}), 200

    except Exception as e:
        logger.error(f"Sensor data error: {e}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# SYSTEM HEALTH AND STATUS
# ============================================================================

@app.route('/api/system/health')
def system_health_api():
    """Get system health status"""
    try:
        if backend:
            health = get_safe_data(lambda: backend.get_system_health(), {'health_status': 'Unknown', 'health_score': 0})
            return jsonify(health)
        else:
            return jsonify({'health_status': 'Legacy Mode', 'health_score': 50})
    except Exception as e:
        logger.error(f"System health error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/system/status')
def system_status():
    """Get system status"""
    try:
        if backend:
            health = get_safe_data(lambda: backend.get_system_health(), {'health_status': 'Unknown'})
            return jsonify(health)
        elif monitoring_core:
            status = get_safe_data(lambda: monitoring_core.get_system_status(), {'status': 'Unknown'})
            return jsonify(status)
        else:
            return jsonify({'status': 'No monitoring system available'}), 503
    except Exception as e:
        logger.error(f"System status error: {e}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# ERROR HANDLERS
# ============================================================================

@app.errorhandler(404)
def not_found_error(error):
    return render_template('base.html'), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f'Server Error: {error}')
    return render_template('base.html'), 500

# ============================================================================
# APPLICATION STARTUP
# ============================================================================

if __name__ == '__main__':
    try:
        print("🍎 Food Monitoring System - Clean Application")
        print("=" * 60)
        print("🔧 Starting integrated frontend and backend system...")
        print()

        if backend:
            print("✅ Backend Services Active:")
            print("   • Settings Manager - System and sensor configuration")
            print("   • Alerts Manager - Real-time alerting and notifications")
            print("   • Reports Manager - Analytics and data export")
        else:
            print("⚠️ Backend services not available - running in demo mode")

        if monitoring_core:
            print("✅ Legacy monitoring core available")

        print()
        
        # Try different ports to find an available one
        ports_to_try = [5000, 8080, 3000, 8000, 5001]
        server_started = False

        for port in ports_to_try:
            try:
                print(f"🌐 Trying to start server on port {port}...")
                print(f"   • Dashboard: http://localhost:{port}")
                print(f"   • Alerts: http://localhost:{port}/alerts")
                print(f"   • API Health: http://localhost:{port}/api/system/health")
                print()
                print("📡 API Endpoints:")
                print(f"   • POST http://localhost:{port}/api/data - Receive sensor data")
                print(f"   • GET http://localhost:{port}/api/dashboard/data - Dashboard data")
                print(f"   • GET http://localhost:{port}/api/system/health - System health")
                print("=" * 60)
                print(f"🚀 COPY THIS URL TO YOUR BROWSER: http://localhost:{port}")
                print("=" * 60)
                print("Press Ctrl+C to stop the server")
                print()

                # Run Flask app with better configuration
                app.run(
                    host='0.0.0.0',
                    port=port,
                    debug=False,
                    use_reloader=False,
                    threaded=True
                )
                server_started = True
                break

            except OSError as e:
                if "Address already in use" in str(e) or "WinError 10048" in str(e):
                    print(f"⚠️ Port {port} is already in use, trying next port...")
                    continue
                else:
                    print(f"❌ Error starting server on port {port}: {e}")
                    continue
            except Exception as e:
                print(f"❌ Unexpected error on port {port}: {e}")
                continue

        if not server_started:
            print("❌ Could not start server on any available port")
            print("Please check if other applications are using these ports")

    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
    finally:
        logger.info("Food Monitoring System stopped")
