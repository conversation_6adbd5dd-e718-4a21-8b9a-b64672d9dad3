#!/usr/bin/env python3
"""
Database Utilities
Database connection and initialization utilities
"""

import os
import sqlite3
import logging
from typing import Optional
from contextlib import contextmanager

from config.settings import get_config


logger = logging.getLogger(__name__)


def get_db_connection(db_path: Optional[str] = None):
    """
    Get database connection
    
    Args:
        db_path: Optional database path override
        
    Returns:
        Database connection
    """
    if db_path is None:
        config = get_config()
        db_url = config.DATABASE_URL
        
        # Extract path from SQLite URL
        if db_url.startswith('sqlite:///'):
            db_path = db_url[10:]  # Remove 'sqlite:///' prefix
        else:
            raise ValueError(f"Unsupported database URL: {db_url}")
    
    # Ensure directory exists
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    # Create connection with optimizations
    conn = sqlite3.connect(
        db_path,
        timeout=30.0,
        check_same_thread=False
    )
    
    # Enable foreign keys and WAL mode for better performance
    conn.execute('PRAGMA foreign_keys = ON')
    conn.execute('PRAGMA journal_mode = WAL')
    conn.execute('PRAGMA synchronous = NORMAL')
    conn.execute('PRAGMA cache_size = 10000')
    conn.execute('PRAGMA temp_store = MEMORY')
    
    return conn


@contextmanager
def get_db_cursor(db_path: Optional[str] = None):
    """
    Context manager for database operations
    
    Args:
        db_path: Optional database path override
        
    Yields:
        Database cursor
    """
    conn = get_db_connection(db_path)
    try:
        cursor = conn.cursor()
        yield cursor
        conn.commit()
    except Exception as e:
        conn.rollback()
        logger.error(f"Database operation failed: {e}")
        raise
    finally:
        conn.close()


def init_database(app=None):
    """
    Initialize database with required tables
    
    Args:
        app: Flask application instance (optional)
    """
    try:
        if app:
            db_url = app.config.get('DATABASE_URL')
        else:
            config = get_config()
            db_url = config.DATABASE_URL
        
        # Extract path from SQLite URL
        if db_url.startswith('sqlite:///'):
            db_path = db_url[10:]
        else:
            raise ValueError(f"Unsupported database URL: {db_url}")
        
        with get_db_cursor(db_path) as cursor:
            # Create sensors table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sensors (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_id TEXT UNIQUE NOT NULL,
                    location TEXT NOT NULL,
                    product_type TEXT,
                    temp_min REAL NOT NULL,
                    temp_max REAL NOT NULL,
                    humidity_min REAL NOT NULL,
                    humidity_max REAL NOT NULL,
                    alert_email TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_seen TIMESTAMP
                )
            ''')
            
            # Create sensor_data table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sensor_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_id TEXT NOT NULL,
                    temperature REAL NOT NULL,
                    humidity REAL NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    location TEXT,
                    product_type TEXT,
                    FOREIGN KEY (device_id) REFERENCES sensors (device_id)
                )
            ''')
            
            # Create alerts table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_id TEXT NOT NULL,
                    alert_type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    status TEXT DEFAULT 'ACTIVE',
                    message TEXT NOT NULL,
                    current_value REAL,
                    threshold_value REAL,
                    location TEXT,
                    product_type TEXT,
                    alert_email TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    acknowledged_at TIMESTAMP,
                    acknowledged_by TEXT,
                    resolved_at TIMESTAMP,
                    resolved_by TEXT,
                    resolution_notes TEXT,
                    escalation_count INTEGER DEFAULT 0,
                    last_escalated_at TIMESTAMP,
                    FOREIGN KEY (device_id) REFERENCES sensors (device_id)
                )
            ''')
            
            # Create users table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    role TEXT DEFAULT 'OPERATOR',
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            ''')
            
            # Create system_settings table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT UNIQUE NOT NULL,
                    value TEXT NOT NULL,
                    description TEXT,
                    category TEXT DEFAULT 'general',
                    data_type TEXT DEFAULT 'string',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_by TEXT
                )
            ''')
            
            # Create audit_log table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS audit_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    action TEXT NOT NULL,
                    resource_type TEXT NOT NULL,
                    resource_id TEXT,
                    details TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Create indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_sensor_data_device_timestamp ON sensor_data (device_id, timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_sensor_data_timestamp ON sensor_data (timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_alerts_device_status ON alerts (device_id, status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_alerts_created_at ON alerts (created_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_alerts_severity ON alerts (severity)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON audit_log (timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON audit_log (user_id)')
            
            # Create triggers for updated_at timestamps
            cursor.execute('''
                CREATE TRIGGER IF NOT EXISTS update_sensors_timestamp 
                AFTER UPDATE ON sensors
                BEGIN
                    UPDATE sensors SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
                END
            ''')
            
            cursor.execute('''
                CREATE TRIGGER IF NOT EXISTS update_users_timestamp 
                AFTER UPDATE ON users
                BEGIN
                    UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
                END
            ''')
            
            cursor.execute('''
                CREATE TRIGGER IF NOT EXISTS update_settings_timestamp 
                AFTER UPDATE ON system_settings
                BEGIN
                    UPDATE system_settings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
                END
            ''')
        
        logger.info("Database initialized successfully")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise


def check_database_health(db_path: Optional[str] = None) -> dict:
    """
    Check database health and return status information
    
    Args:
        db_path: Optional database path override
        
    Returns:
        Dictionary with health information
    """
    try:
        with get_db_cursor(db_path) as cursor:
            # Check if main tables exist
            tables = ['sensors', 'sensor_data', 'alerts', 'users', 'system_settings']
            table_status = {}
            
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                table_status[table] = count
            
            # Check database size
            cursor.execute("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")
            db_size = cursor.fetchone()[0]
            
            # Check for any corruption
            cursor.execute("PRAGMA integrity_check")
            integrity = cursor.fetchone()[0]
            
            return {
                'status': 'healthy' if integrity == 'ok' else 'corrupted',
                'tables': table_status,
                'size_bytes': db_size,
                'integrity_check': integrity
            }
            
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            'status': 'error',
            'error': str(e)
        }


def backup_database(db_path: Optional[str] = None, backup_path: Optional[str] = None) -> bool:
    """
    Create a backup of the database
    
    Args:
        db_path: Source database path
        backup_path: Backup file path
        
    Returns:
        True if successful, False otherwise
    """
    try:
        import shutil
        from datetime import datetime
        
        if db_path is None:
            config = get_config()
            db_url = config.DATABASE_URL
            if db_url.startswith('sqlite:///'):
                db_path = db_url[10:]
            else:
                raise ValueError(f"Unsupported database URL: {db_url}")
        
        if backup_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = f"data/backups/monitoring_backup_{timestamp}.db"
        
        # Ensure backup directory exists
        os.makedirs(os.path.dirname(backup_path), exist_ok=True)
        
        # Create backup
        shutil.copy2(db_path, backup_path)
        
        logger.info(f"Database backup created: {backup_path}")
        return True
        
    except Exception as e:
        logger.error(f"Database backup failed: {e}")
        return False
