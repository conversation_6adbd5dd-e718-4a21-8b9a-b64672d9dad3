#!/usr/bin/env python3
"""
Pytest Configuration and Fixtures
Shared test configuration and fixtures for the monitoring system tests
"""

import os
import pytest
import tempfile
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

# Set test environment
os.environ['FLASK_ENV'] = 'testing'

from app import create_app
from app.utils.database import init_database
from app.core.repositories.sensor_repository import SensorRepository
from app.core.repositories.alert_repository import AlertRepository
from app.models.enums import AlertType, AlertSeverity, AlertStatus, SensorStatus


@pytest.fixture(scope='session')
def app():
    """Create application for testing"""
    # Create temporary database file
    db_fd, db_path = tempfile.mkstemp()
    
    # Override configuration for testing
    test_config = {
        'TESTING': True,
        'DATABASE_URL': f'sqlite:///{db_path}',
        'WTF_CSRF_ENABLED': False,
        'SECRET_KEY': 'test-secret-key'
    }
    
    # Create app with test configuration
    app = create_app('testing')
    app.config.update(test_config)
    
    # Initialize database
    with app.app_context():
        init_database(app)
    
    yield app
    
    # Cleanup
    os.close(db_fd)
    os.unlink(db_path)


@pytest.fixture
def client(app):
    """Create test client"""
    return app.test_client()


@pytest.fixture
def runner(app):
    """Create test CLI runner"""
    return app.test_cli_runner()


@pytest.fixture
def app_context(app):
    """Create application context"""
    with app.app_context():
        yield app


@pytest.fixture
def request_context(app):
    """Create request context"""
    with app.test_request_context():
        yield


@pytest.fixture
def sensor_repository(app_context):
    """Create sensor repository for testing"""
    return SensorRepository()


@pytest.fixture
def alert_repository(app_context):
    """Create alert repository for testing"""
    return AlertRepository()


@pytest.fixture
def sample_sensor_config():
    """Sample sensor configuration for testing"""
    return {
        'device_id': 'TEST_SENSOR_001',
        'location': 'Test Cold Storage',
        'product_type': 'FRESH_MEAT',
        'temp_min': -2.0,
        'temp_max': 4.0,
        'humidity_min': 80.0,
        'humidity_max': 95.0,
        'alert_email': '<EMAIL>',
        'is_active': True
    }


@pytest.fixture
def sample_sensor_data():
    """Sample sensor data for testing"""
    return {
        'device_id': 'TEST_SENSOR_001',
        'temperature': 2.5,
        'humidity': 85.0,
        'timestamp': datetime.now(),
        'location': 'Test Cold Storage',
        'product_type': 'FRESH_MEAT'
    }


@pytest.fixture
def sample_alert_data():
    """Sample alert data for testing"""
    return {
        'device_id': 'TEST_SENSOR_001',
        'alert_type': AlertType.TEMPERATURE_HIGH,
        'severity': AlertSeverity.HIGH,
        'status': AlertStatus.ACTIVE,
        'message': 'Temperature exceeded safe limits',
        'current_value': 6.5,
        'threshold_value': 4.0,
        'location': 'Test Cold Storage',
        'product_type': 'FRESH_MEAT',
        'created_at': datetime.now()
    }


@pytest.fixture
def multiple_sensors():
    """Multiple sensor configurations for testing"""
    return [
        {
            'device_id': 'SENSOR_001',
            'location': 'Cold Storage A',
            'product_type': 'FRESH_MEAT',
            'temp_min': -2.0,
            'temp_max': 4.0,
            'humidity_min': 80.0,
            'humidity_max': 95.0,
            'alert_email': '<EMAIL>',
            'is_active': True
        },
        {
            'device_id': 'SENSOR_002',
            'location': 'Dairy Refrigerator',
            'product_type': 'DAIRY_PRODUCTS',
            'temp_min': 1.0,
            'temp_max': 4.0,
            'humidity_min': 75.0,
            'humidity_max': 85.0,
            'alert_email': '<EMAIL>',
            'is_active': True
        },
        {
            'device_id': 'SENSOR_003',
            'location': 'Freezer Unit',
            'product_type': 'FROZEN_FOODS',
            'temp_min': -25.0,
            'temp_max': -18.0,
            'humidity_min': 70.0,
            'humidity_max': 90.0,
            'alert_email': '<EMAIL>',
            'is_active': False
        }
    ]


@pytest.fixture
def time_series_data():
    """Time series sensor data for testing"""
    base_time = datetime.now() - timedelta(hours=24)
    data = []
    
    for i in range(24):  # 24 hours of hourly data
        timestamp = base_time + timedelta(hours=i)
        data.append({
            'device_id': 'TEST_SENSOR_001',
            'temperature': 2.0 + (i % 3),  # Varying temperature
            'humidity': 80.0 + (i % 10),   # Varying humidity
            'timestamp': timestamp
        })
    
    return data


@pytest.fixture
def mock_email_service():
    """Mock email service for testing notifications"""
    with patch('app.core.services.notification_service.EmailService') as mock:
        mock_instance = Mock()
        mock.return_value = mock_instance
        mock_instance.send_email.return_value = True
        yield mock_instance


@pytest.fixture
def mock_redis():
    """Mock Redis for testing caching"""
    with patch('redis.Redis') as mock:
        mock_instance = Mock()
        mock.return_value = mock_instance
        mock_instance.get.return_value = None
        mock_instance.set.return_value = True
        mock_instance.delete.return_value = True
        yield mock_instance


@pytest.fixture
def authenticated_client(client, app):
    """Client with authenticated session"""
    with client.session_transaction() as sess:
        sess['user_id'] = 1
        sess['username'] = 'test_user'
        sess['role'] = 'ADMIN'
    return client


# Test data generators
def generate_sensor_readings(device_id, count=10, start_time=None):
    """Generate test sensor readings"""
    if start_time is None:
        start_time = datetime.now() - timedelta(hours=count)
    
    readings = []
    for i in range(count):
        timestamp = start_time + timedelta(hours=i)
        readings.append({
            'device_id': device_id,
            'temperature': 2.0 + (i % 5),
            'humidity': 80.0 + (i % 15),
            'timestamp': timestamp
        })
    
    return readings


def generate_alerts(device_id, count=5, start_time=None):
    """Generate test alerts"""
    if start_time is None:
        start_time = datetime.now() - timedelta(hours=count)
    
    alerts = []
    severities = [AlertSeverity.LOW, AlertSeverity.MEDIUM, AlertSeverity.HIGH, AlertSeverity.CRITICAL]
    alert_types = [AlertType.TEMPERATURE_HIGH, AlertType.TEMPERATURE_LOW, 
                   AlertType.HUMIDITY_HIGH, AlertType.HUMIDITY_LOW]
    
    for i in range(count):
        timestamp = start_time + timedelta(hours=i)
        alerts.append({
            'device_id': device_id,
            'alert_type': alert_types[i % len(alert_types)],
            'severity': severities[i % len(severities)],
            'status': AlertStatus.ACTIVE if i < 2 else AlertStatus.RESOLVED,
            'message': f'Test alert {i+1}',
            'current_value': 5.0 + i,
            'threshold_value': 4.0,
            'created_at': timestamp
        })
    
    return alerts


# Pytest markers
pytest_plugins = []

# Custom markers
def pytest_configure(config):
    """Configure custom pytest markers"""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "e2e: mark test as an end-to-end test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "api: mark test as API test"
    )
    config.addinivalue_line(
        "markers", "web: mark test as web interface test"
    )


# Test utilities
class TestDataBuilder:
    """Builder pattern for creating test data"""
    
    @staticmethod
    def sensor_config(device_id='TEST_SENSOR', **kwargs):
        """Build sensor configuration"""
        defaults = {
            'device_id': device_id,
            'location': 'Test Location',
            'product_type': 'FRESH_MEAT',
            'temp_min': -2.0,
            'temp_max': 4.0,
            'humidity_min': 80.0,
            'humidity_max': 95.0,
            'alert_email': '<EMAIL>',
            'is_active': True
        }
        defaults.update(kwargs)
        return defaults
    
    @staticmethod
    def sensor_reading(device_id='TEST_SENSOR', **kwargs):
        """Build sensor reading"""
        defaults = {
            'device_id': device_id,
            'temperature': 2.5,
            'humidity': 85.0,
            'timestamp': datetime.now()
        }
        defaults.update(kwargs)
        return defaults
    
    @staticmethod
    def alert(device_id='TEST_SENSOR', **kwargs):
        """Build alert"""
        defaults = {
            'device_id': device_id,
            'alert_type': AlertType.TEMPERATURE_HIGH,
            'severity': AlertSeverity.HIGH,
            'status': AlertStatus.ACTIVE,
            'message': 'Test alert',
            'current_value': 6.0,
            'threshold_value': 4.0,
            'created_at': datetime.now()
        }
        defaults.update(kwargs)
        return defaults


# Export test utilities
__all__ = [
    'TestDataBuilder',
    'generate_sensor_readings',
    'generate_alerts'
]
