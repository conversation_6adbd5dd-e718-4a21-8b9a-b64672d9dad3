import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from database import DatabaseManager
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import json

class MonitoringCore:
    """Core monitoring system for temperature and humidity"""

    def __init__(self, config_file: str = "monitoring_app/config.json"):
        self.db = DatabaseManager()
        self.config = self._load_config(config_file)
        self.running = False
        self.monitor_thread = None

        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('monitoring_app/monitoring.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def _load_config(self, config_file: str) -> Dict:
        """Load configuration from file"""
        default_config = {
            "email": {
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "username": "",
                "password": "",
                "from_email": ""
            },
            "monitoring": {
                "check_interval": 60,  # seconds
                "alert_cooldown": 300,  # seconds
                "data_retention_days": 30
            },
            "thresholds": {
                "default_temp_min": 0.0,
                "default_temp_max": 8.0,
                "default_humidity_min": 75.0,
                "default_humidity_max": 90.0
            }
        }

        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
                # Merge with defaults
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                    elif isinstance(value, dict):
                        for subkey, subvalue in value.items():
                            if subkey not in config[key]:
                                config[key][subkey] = subvalue
                return config
        except FileNotFoundError:
            self.logger.info(f"Config file {config_file} not found, using defaults")
            self._save_config(config_file, default_config)
            return default_config
        except Exception as e:
            self.logger.error(f"Error loading config: {e}")
            return default_config

    def _save_config(self, config_file: str, config: Dict):
        """Save configuration to file"""
        try:
            import os
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=4)
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")

    def start_monitoring(self):
        """Start the monitoring system"""
        if self.running:
            self.logger.warning("Monitoring is already running")
            return

        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.logger.info("Monitoring system started")

    def stop_monitoring(self):
        """Stop the monitoring system"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("Monitoring system stopped")

    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.running:
            try:
                self._check_all_sensors()
                self._cleanup_old_data()
                time.sleep(self.config['monitoring']['check_interval'])
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(10)  # Wait before retrying

    def _check_all_sensors(self):
        """Check all active sensors for threshold violations"""
        sensor_configs = self.db.get_sensor_config()

        for config in sensor_configs:
            if not config['is_active']:
                continue

            # Get latest reading for this sensor
            recent_data = self.db.get_sensor_data(config['device_id'], hours=1)

            if not recent_data:
                self._handle_sensor_offline(config)
                continue

            latest_reading = recent_data[0]
            self._check_thresholds(latest_reading, config)

    def _check_thresholds(self, reading: Dict, config: Dict):
        """Check if reading violates thresholds"""
        device_id = reading['device_id']
        temperature = reading['temperature']
        humidity = reading['humidity']

        # Check temperature thresholds
        if temperature < config['temp_min']:
            self._create_alert(
                device_id, "TEMPERATURE_LOW", "HIGH",
                f"Temperature {temperature}°C is below minimum threshold {config['temp_min']}°C",
                temperature, config['temp_min']
            )
        elif temperature > config['temp_max']:
            self._create_alert(
                device_id, "TEMPERATURE_HIGH", "HIGH",
                f"Temperature {temperature}°C is above maximum threshold {config['temp_max']}°C",
                temperature, config['temp_max']
            )

        # Check humidity thresholds
        if humidity < config['humidity_min']:
            self._create_alert(
                device_id, "HUMIDITY_LOW", "MEDIUM",
                f"Humidity {humidity}% is below minimum threshold {config['humidity_min']}%",
                humidity, config['humidity_min']
            )
        elif humidity > config['humidity_max']:
            self._create_alert(
                device_id, "HUMIDITY_HIGH", "MEDIUM",
                f"Humidity {humidity}% is above maximum threshold {config['humidity_max']}%",
                humidity, config['humidity_max']
            )

    def _create_alert(self, device_id: str, alert_type: str, severity: str,
                     message: str, current_value: float = None,
                     threshold_value: float = None):
        """Create and process an alert"""
        # Check if similar alert exists recently (cooldown)
        if self._is_alert_in_cooldown(device_id, alert_type):
            return

        # Insert alert into database
        success = self.db.insert_alert(
            device_id, alert_type, severity, message,
            current_value, threshold_value
        )

        if success:
            self.logger.warning(f"ALERT: {message}")

            # Send email notification if configured
            sensor_config = self.db.get_sensor_config(device_id)
            if sensor_config and sensor_config.get('alert_email'):
                self._send_email_alert(sensor_config['alert_email'], message, severity)

    def _is_alert_in_cooldown(self, device_id: str, alert_type: str) -> bool:
        """Check if similar alert was sent recently"""
        cooldown_minutes = self.config['monitoring']['alert_cooldown'] // 60

        import sqlite3
        with sqlite3.connect(self.db.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT COUNT(*) FROM alerts
                WHERE device_id = ? AND alert_type = ?
                AND created_at >= datetime('now', '-{} minutes')
            '''.format(cooldown_minutes), (device_id, alert_type))

            count = cursor.fetchone()[0]
            return count > 0

    def _send_email_alert(self, to_email: str, message: str, severity: str):
        """Send email alert notification"""
        try:
            email_config = self.config['email']
            if not all([email_config['username'], email_config['password'], email_config['from_email']]):
                self.logger.warning("Email configuration incomplete, skipping email alert")
                return

            msg = MIMEMultipart()
            msg['From'] = email_config['from_email']
            msg['To'] = to_email
            msg['Subject'] = f"[{severity}] Food Monitoring Alert"

            body = f"""
            Food Monitoring System Alert

            Severity: {severity}
            Message: {message}
            Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

            Please check the monitoring dashboard for more details.
            """

            msg.attach(MIMEText(body, 'plain'))

            server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
            server.starttls()
            server.login(email_config['username'], email_config['password'])
            text = msg.as_string()
            server.sendmail(email_config['from_email'], to_email, text)
            server.quit()

            self.logger.info(f"Email alert sent to {to_email}")

        except Exception as e:
            self.logger.error(f"Failed to send email alert: {e}")

    def _handle_sensor_offline(self, config: Dict):
        """Handle offline sensor"""
        device_id = config['device_id']
        self._create_alert(
            device_id, "SENSOR_OFFLINE", "HIGH",
            f"Sensor {device_id} at {config['location']} is offline - no data received in the last hour"
        )

    def _cleanup_old_data(self):
        """Clean up old data based on retention policy"""
        try:
            retention_days = self.config['monitoring']['data_retention_days']

            import sqlite3
            with sqlite3.connect(self.db.db_path) as conn:
                cursor = conn.cursor()

                # Clean old sensor data
                cursor.execute('''
                    DELETE FROM sensor_data
                    WHERE created_at < datetime('now', '-{} days')
                '''.format(retention_days))

                # Clean old acknowledged alerts
                cursor.execute('''
                    DELETE FROM alerts
                    WHERE is_acknowledged = 1
                    AND acknowledged_at < datetime('now', '-{} days')
                '''.format(retention_days))

                # Clean old logs
                cursor.execute('''
                    DELETE FROM system_logs
                    WHERE created_at < datetime('now', '-{} days')
                '''.format(retention_days))

                conn.commit()

        except Exception as e:
            self.logger.error(f"Error during data cleanup: {e}")

    def process_sensor_data(self, data: Dict) -> bool:
        """Process incoming sensor data"""
        try:
            # Validate data
            required_fields = ['device_id', 'temperature', 'humidity', 'location', 'timestamp']
            if not all(field in data for field in required_fields):
                self.logger.error(f"Invalid sensor data: missing required fields")
                return False

            # Insert data into database
            success = self.db.insert_sensor_data(data)

            if success:
                self.logger.debug(f"Processed data from {data['device_id']}: T={data['temperature']}°C, H={data['humidity']}%")

                # Immediate threshold check for this reading
                sensor_config = self.db.get_sensor_config(data['device_id'])
                if sensor_config:
                    self._check_thresholds(data, sensor_config)

            return success

        except Exception as e:
            self.logger.error(f"Error processing sensor data: {e}")
            return False

    def get_system_status(self) -> Dict:
        """Get current system status"""
        try:
            active_alerts = self.db.get_active_alerts()
            sensor_configs = self.db.get_sensor_config()

            # Get recent data for each sensor
            sensor_status = []
            for config in sensor_configs:
                recent_data = self.db.get_sensor_data(config['device_id'], hours=1)
                last_reading = recent_data[0] if recent_data else None

                sensor_status.append({
                    'device_id': config['device_id'],
                    'location': config['location'],
                    'product_type': config.get('product_type'),
                    'is_active': config['is_active'],
                    'last_reading': last_reading,
                    'status': 'online' if last_reading else 'offline'
                })

            return {
                'monitoring_active': self.running,
                'total_sensors': len(sensor_configs),
                'active_alerts': len(active_alerts),
                'sensor_status': sensor_status,
                'alerts': active_alerts
            }

        except Exception as e:
            self.logger.error(f"Error getting system status: {e}")
            return {'error': str(e)}