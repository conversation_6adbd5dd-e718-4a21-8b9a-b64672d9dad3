#!/usr/bin/env python3
"""
Test Script for Food Monitoring System Backend
Demonstrates the functionality of all backend components
"""

import sys
import os
import time
from datetime import datetime

# Add the parent directory to the path so we can import the backend
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend import BackendManager, AlertType, AlertSeverity

def test_backend_functionality():
    """Test all backend functionality"""
    print("🍎 Food Monitoring System - Backend Test")
    print("=" * 60)

    # Initialize backend manager
    print("📊 Initializing backend manager...")
    backend = BackendManager("test_monitoring.db")
    print("✅ Backend manager initialized successfully")

    # Test Settings Manager
    print("\n⚙️ Testing Settings Manager...")
    test_settings_manager(backend)

    # Test Alerts Manager
    print("\n🚨 Testing Alerts Manager...")
    test_alerts_manager(backend)

    # Test Reports Manager
    print("\n📈 Testing Reports Manager...")
    test_reports_manager(backend)

    # Test Integrated Functionality
    print("\n🔗 Testing Integrated Functionality...")
    test_integrated_functionality(backend)

    # Cleanup
    print("\n🧹 Cleaning up...")
    backend.shutdown()
    print("✅ Backend test completed successfully!")

def test_settings_manager(backend):
    """Test settings management functionality"""
    try:
        # Test getting all settings
        settings = backend.settings.get_all_settings()
        print(f"✅ Retrieved {len(settings)} system settings")

        # Test updating a setting
        success = backend.update_system_setting('company_name', 'Test Food Company', 'test_user')
        print(f"✅ Setting update: {'Success' if success else 'Failed'}")

        # Test adding a sensor configuration
        sensor_config = {
            'device_id': 'TEST_SENSOR_001',
            'location': 'Test Cold Storage',
            'product_type': 'Fresh Meat',
            'temp_min': -2.0,
            'temp_max': 4.0,
            'humidity_min': 80.0,
            'humidity_max': 95.0,
            'alert_email': '<EMAIL>',
            'is_active': True
        }

        success = backend.add_sensor(sensor_config)
        print(f"✅ Sensor addition: {'Success' if success else 'Failed'}")

        # Test getting sensor configurations
        sensors = backend.settings.get_sensor_config()
        print(f"✅ Retrieved {len(sensors)} sensor configurations")

    except Exception as e:
        print(f"❌ Settings test error: {e}")

def test_alerts_manager(backend):
    """Test alerts management functionality"""
    try:
        # Test creating alerts
        alert_id = backend.create_sensor_alert(
            device_id='TEST_SENSOR_001',
            alert_type='TEMPERATURE_HIGH',
            severity='HIGH',
            message='Test temperature alert - temperature too high',
            current_value=6.5,
            threshold_value=4.0
        )
        print(f"✅ Created alert with ID: {alert_id}")

        # Create another alert
        alert_id2 = backend.create_sensor_alert(
            device_id='TEST_SENSOR_001',
            alert_type='HUMIDITY_LOW',
            severity='MEDIUM',
            message='Test humidity alert - humidity too low',
            current_value=75.0,
            threshold_value=80.0
        )
        print(f"✅ Created alert with ID: {alert_id2}")

        # Test getting alerts
        active_alerts = backend.alerts.get_alerts(acknowledged=False)
        print(f"✅ Retrieved {len(active_alerts)} active alerts")

        # Test acknowledging an alert
        if alert_id:
            success = backend.acknowledge_alert(alert_id, 'test_user')
            print(f"✅ Alert acknowledgment: {'Success' if success else 'Failed'}")

        # Test alert statistics
        stats = backend.alerts.get_alert_statistics(hours=24)
        print(f"✅ Alert statistics: {stats.get('total_alerts', 0)} total alerts")

    except Exception as e:
        print(f"❌ Alerts test error: {e}")

if __name__ == "__main__":
    # Run basic functionality tests
    test_backend_functionality()

    print("\n🎉 All backend tests completed!")
    print("The backend system is ready for integration with the Flask application.")