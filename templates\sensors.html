{% extends "base.html" %}

{% block title %}Sensors - Food Monitoring System{% endblock %}

{% block content %}
<div class="card">
    <h2>🔧 Sensor Management</h2>
    <p>Configure and manage temperature and humidity sensors for monitoring perishable goods.</p>

    <div class="text-right mb-2">
        <a href="/sensor/add" class="btn btn-success">➕ Add New Sensor</a>
    </div>

    {% if sensors %}
        <table class="table">
            <thead>
                <tr>
                    <th>Device ID</th>
                    <th>Location</th>
                    <th>Product Type</th>
                    <th>Temperature Range</th>
                    <th>Humidity Range</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for sensor in sensors %}
                <tr>
                    <td><strong>{{ sensor.device_id }}</strong></td>
                    <td>{{ sensor.location }}</td>
                    <td>{{ sensor.product_type or 'Not specified' }}</td>
                    <td>{{ sensor.temp_min }}°C to {{ sensor.temp_max }}°C</td>
                    <td>{{ sensor.humidity_min }}% to {{ sensor.humidity_max }}%</td>
                    <td>
                        {% if sensor.is_active %}
                            <span class="status-badge status-online">ACTIVE</span>
                        {% else %}
                            <span class="status-badge status-offline">INACTIVE</span>
                        {% endif %}
                    </td>
                    <td>
                        <a href="/sensor/{{ sensor.device_id }}" class="btn" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">📊 View</a>
                        <a href="/sensor/{{ sensor.device_id }}/edit" class="btn" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">✏️ Edit</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <div class="alert alert-info">
            <h4>No sensors configured</h4>
            <p>Get started by adding your first sensor to begin monitoring temperature and humidity conditions.</p>
            <a href="/sensor/add" class="btn btn-success mt-2">➕ Add Your First Sensor</a>
        </div>
    {% endif %}
</div>

<div class="card">
    <h3>📋 Product Type Reference</h3>
    <p>Recommended temperature and humidity ranges for different product types:</p>

    {% if product_types %}
        <table class="table">
            <thead>
                <tr>
                    <th>Product Type</th>
                    <th>Description</th>
                    <th>Temperature Range</th>
                    <th>Humidity Range</th>
                    <th>Shelf Life</th>
                </tr>
            </thead>
            <tbody>
                {% for product in product_types %}
                <tr>
                    <td><strong>{{ product.name }}</strong></td>
                    <td>{{ product.description }}</td>
                    <td>{{ product.temp_min }}°C to {{ product.temp_max }}°C</td>
                    <td>{{ product.humidity_min }}% to {{ product.humidity_max }}%</td>
                    <td>{{ product.shelf_life_hours }} hours</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    {% endif %}
</div>
{% endblock %}