{% extends "base.html" %}

{% block title %}Alerts - Food Monitoring System{% endblock %}

{% block extra_css %}
<style>
    .alert-item {
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 8px;
        border-left: 4px solid;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .alert-high {
        background-color: #f8d7da;
        border-color: #dc3545;
        color: #721c24;
    }

    .alert-medium {
        background-color: #fff3cd;
        border-color: #ffc107;
        color: #856404;
    }

    .alert-low {
        background-color: #d1ecf1;
        border-color: #17a2b8;
        color: #0c5460;
    }

    .alert-content {
        flex-grow: 1;
    }

    .alert-actions {
        margin-left: 1rem;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-box {
        text-align: center;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 10px;
        border: 1px solid #dee2e6;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #667eea;
        display: block;
    }

    .stat-label {
        color: #666;
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .filter-section {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
    }

    .filter-row {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
    }

    .filter-item {
        display: flex;
        flex-direction: column;
        min-width: 150px;
    }

    .filter-item label {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
        color: #666;
    }

    .filter-item select, .filter-item input {
        padding: 0.5rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    function acknowledgeAlert(alertId) {
        const acknowledgedBy = prompt("Enter your name:") || "System User";

        fetch(`/api/alert/${alertId}/acknowledge`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({acknowledged_by: acknowledgedBy})
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                location.reload();
            } else {
                alert('Failed to acknowledge alert: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            alert('Error: ' + error);
        });
    }

    function filterAlerts() {
        const severity = document.getElementById('severityFilter').value;
        const device = document.getElementById('deviceFilter').value;
        const alertType = document.getElementById('typeFilter').value;

        const alerts = document.querySelectorAll('.alert-item');

        alerts.forEach(alert => {
            let show = true;

            if (severity && !alert.classList.contains(`alert-${severity.toLowerCase()}`)) {
                show = false;
            }

            if (device && !alert.dataset.device.includes(device)) {
                show = false;
            }

            if (alertType && !alert.dataset.type.includes(alertType)) {
                show = false;
            }

            alert.style.display = show ? 'flex' : 'none';
        });
    }

    // Auto-refresh every 60 seconds
    setTimeout(() => location.reload(), 60000);
</script>
{% endblock %}

{% block content %}
<div class="card">
    <h2>🚨 Alert Management</h2>
    <p>Monitor and manage system alerts and notifications for temperature and humidity violations.</p>

    <!-- Alert Statistics -->
    <div class="stats-grid">
        <div class="stat-box">
            <span class="stat-number">{{ alerts|length }}</span>
            <div class="stat-label">Active Alerts</div>
        </div>
        <div class="stat-box">
            <span class="stat-number" style="color: #dc3545;">
                {% set high_alerts = alerts|selectattr('severity', 'equalto', 'HIGH')|list %}
                {{ high_alerts|length }}
            </span>
            <div class="stat-label">High Priority</div>
        </div>
        <div class="stat-box">
            <span class="stat-number" style="color: #ffc107;">
                {% set medium_alerts = alerts|selectattr('severity', 'equalto', 'MEDIUM')|list %}
                {{ medium_alerts|length }}
            </span>
            <div class="stat-label">Medium Priority</div>
        </div>
        <div class="stat-box">
            <span class="stat-number" style="color: #17a2b8;">
                {% set low_alerts = alerts|selectattr('severity', 'equalto', 'LOW')|list %}
                {{ low_alerts|length }}
            </span>
            <div class="stat-label">Low Priority</div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
        <h4>🔍 Filter Alerts</h4>
        <div class="filter-row">
            <div class="filter-item">
                <label>Severity:</label>
                <select id="severityFilter" onchange="filterAlerts()">
                    <option value="">All Severities</option>
                    <option value="HIGH">High</option>
                    <option value="MEDIUM">Medium</option>
                    <option value="LOW">Low</option>
                </select>
            </div>
            <div class="filter-item">
                <label>Device:</label>
                <select id="deviceFilter" onchange="filterAlerts()">
                    <option value="">All Devices</option>
                    {% for alert in alerts %}
                        <option value="{{ alert.device_id }}">{{ alert.device_id }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="filter-item">
                <label>Alert Type:</label>
                <select id="typeFilter" onchange="filterAlerts()">
                    <option value="">All Types</option>
                    <option value="TEMPERATURE_HIGH">Temperature High</option>
                    <option value="TEMPERATURE_LOW">Temperature Low</option>
                    <option value="HUMIDITY_HIGH">Humidity High</option>
                    <option value="HUMIDITY_LOW">Humidity Low</option>
                    <option value="SENSOR_OFFLINE">Sensor Offline</option>
                </select>
            </div>
            <div class="filter-item">
                <button class="btn" onclick="location.reload()">🔄 Refresh</button>
            </div>
        </div>
    </div>
</div>

<!-- Active Alerts -->
<div class="card">
    <h3>🔴 Active Alerts ({{ alerts|length }})</h3>
    {% if alerts %}
        {% for alert in alerts %}
            <div class="alert-item alert-{{ alert.severity.lower() }}"
                 data-device="{{ alert.device_id }}"
                 data-type="{{ alert.alert_type }}">
                <div class="alert-content">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 0.5rem;">
                        <strong style="font-size: 1.1rem;">{{ alert.device_id }}</strong>
                        <span style="background: rgba(0,0,0,0.1); padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem; margin-left: 1rem;">
                            {{ alert.severity }}
                        </span>
                    </div>
                    <div style="margin-bottom: 0.5rem;">
                        <strong>{{ alert.alert_type.replace('_', ' ').title() }}</strong>
                    </div>
                    <div style="margin-bottom: 0.5rem;">
                        {{ alert.message }}
                    </div>
                    {% if alert.current_value and alert.threshold_value %}
                        <div style="font-size: 0.9rem; color: #666;">
                            Current: <strong>{{ alert.current_value }}</strong> |
                            Threshold: <strong>{{ alert.threshold_value }}</strong>
                        </div>
                    {% endif %}
                    <div style="font-size: 0.8rem; color: #666; margin-top: 0.5rem;">
                        📅 {{ alert.created_at }}
                    </div>
                </div>
                <div class="alert-actions">
                    <button class="btn btn-success" onclick="acknowledgeAlert({{ alert.id }})">
                        ✓ Acknowledge
                    </button>
                </div>
            </div>
        {% endfor %}
    {% else %}
        <div style="text-align: center; padding: 3rem; color: #28a745;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">✅</div>
            <h4>No Active Alerts</h4>
            <p>All systems are operating within normal parameters.</p>
        </div>
    {% endif %}
</div>

<!-- Alert Types Information -->
<div class="card">
    <h3>📋 Alert Types Reference</h3>
    <div class="table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th>Alert Type</th>
                    <th>Description</th>
                    <th>Typical Causes</th>
                    <th>Recommended Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Temperature High</strong></td>
                    <td>Temperature exceeds maximum threshold</td>
                    <td>Cooling system failure, door left open, high ambient temperature</td>
                    <td>Check cooling system, verify door seals, inspect ventilation</td>
                </tr>
                <tr>
                    <td><strong>Temperature Low</strong></td>
                    <td>Temperature below minimum threshold</td>
                    <td>Overcooling, thermostat malfunction, sensor error</td>
                    <td>Adjust thermostat, check sensor calibration, verify settings</td>
                </tr>
                <tr>
                    <td><strong>Humidity High</strong></td>
                    <td>Humidity exceeds maximum threshold</td>
                    <td>Poor ventilation, water leaks, high ambient humidity</td>
                    <td>Improve ventilation, check for leaks, use dehumidifier</td>
                </tr>
                <tr>
                    <td><strong>Humidity Low</strong></td>
                    <td>Humidity below minimum threshold</td>
                    <td>Excessive ventilation, dry conditions, heating issues</td>
                    <td>Reduce ventilation, add humidity source, check heating system</td>
                </tr>
                <tr>
                    <td><strong>Sensor Offline</strong></td>
                    <td>No data received from sensor</td>
                    <td>Power failure, network issues, sensor malfunction</td>
                    <td>Check power supply, verify network connection, inspect sensor</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Quick Actions -->
<div class="card">
    <h3>⚡ Quick Actions</h3>
    <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
        <button class="btn" onclick="location.reload()">🔄 Refresh Alerts</button>
        <button class="btn" onclick="window.open('/reports', '_blank')">📈 View Reports</button>
        <button class="btn" onclick="window.open('/settings', '_blank')">⚙️ System Settings</button>
        <button class="btn" onclick="window.open('/', '_blank')">📊 Dashboard</button>
    </div>
</div>
{% endblock %}