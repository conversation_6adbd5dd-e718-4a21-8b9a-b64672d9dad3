#!/usr/bin/env python3
"""
Flask Application with Integrated Backend
Food Monitoring System with comprehensive backend integration
"""

from flask import Flask, render_template, jsonify, request, redirect, url_for, flash, Response
import logging
from datetime import datetime
import json

# Import our backend system
from backend import BackendManager, AlertType, AlertSeverity

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.secret_key = 'food-monitoring-secret-key-change-this'

# Initialize backend manager
backend = BackendManager("food_monitoring.db")

# ============================================================================
# DASHBOARD ROUTES WITH BACKEND INTEGRATION
# ============================================================================

@app.route('/')
def dashboard():
    """Enhanced dashboard with backend data"""
    try:
        # Get comprehensive dashboard data from backend
        dashboard_data = backend.get_dashboard_data()

        # Get system health
        system_health = backend.get_system_health()

        return render_template('dashboard.html',
                             overview=dashboard_data.get('overview', {}),
                             alerts=dashboard_data.get('active_alerts', []),
                             sensor_statistics=dashboard_data.get('sensor_statistics', []),
                             system_health=system_health,
                             current_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    except Exception as e:
        logger.error(f"Dashboard error: {e}")
        flash(f"Dashboard error: {e}", 'error')
        return render_template('dashboard.html',
                             overview={}, alerts=[], sensor_statistics=[],
                             system_health={'health_status': 'Error'})

@app.route('/api/dashboard/data')
def dashboard_api():
    """API endpoint for dashboard data"""
    try:
        data = backend.get_dashboard_data()
        return jsonify(data)
    except Exception as e:
        logger.error(f"Dashboard API error: {e}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# ALERTS ROUTES WITH BACKEND INTEGRATION
# ============================================================================

@app.route('/alerts')
def alerts_page():
    """Enhanced alerts page with backend data"""
    try:
        # Get active alerts from backend
        active_alerts = backend.alerts.get_alerts(acknowledged=False, limit=100)

        # Get alert statistics
        alert_stats = backend.alerts.get_alert_statistics(hours=24)

        return render_template('alerts.html',
                             alerts=active_alerts,
                             alert_statistics=alert_stats)
    except Exception as e:
        logger.error(f"Alerts page error: {e}")
        flash(f"Error loading alerts: {e}", 'error')
        return render_template('alerts.html', alerts=[], alert_statistics={})

@app.route('/api/alert/<int:alert_id>/acknowledge', methods=['POST'])
def acknowledge_alert_api(alert_id):
    """Enhanced alert acknowledgment with backend"""
    try:
        data = request.get_json()
        acknowledged_by = data.get('acknowledged_by', 'System User')

        success = backend.acknowledge_alert(alert_id, acknowledged_by)

        if success:
            return jsonify({'status': 'success', 'message': 'Alert acknowledged'})
        else:
            return jsonify({'error': 'Failed to acknowledge alert'}), 500

    except Exception as e:
        logger.error(f"Alert acknowledgment error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/alerts/create', methods=['POST'])
def create_alert_api():
    """API endpoint to create alerts"""
    try:
        data = request.get_json()

        alert_id = backend.create_sensor_alert(
            device_id=data['device_id'],
            alert_type=data['alert_type'],
            severity=data['severity'],
            message=data['message'],
            current_value=data.get('current_value'),
            threshold_value=data.get('threshold_value')
        )

        if alert_id:
            return jsonify({'status': 'success', 'alert_id': alert_id})
        else:
            return jsonify({'error': 'Failed to create alert'}), 500

    except Exception as e:
        logger.error(f"Alert creation error: {e}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# SETTINGS ROUTES WITH BACKEND INTEGRATION
# ============================================================================

@app.route('/settings')
def settings_page():
    """Enhanced settings page with backend configuration"""
    try:
        # Get all settings from backend
        settings = backend.settings.get_all_settings()

        # Get sensor configurations
        sensors = backend.settings.get_sensor_config()

        # Default product types
        product_types = [
            {'name': 'Fresh Meat', 'description': 'Fresh meat products', 'temp_min': -2, 'temp_max': 4, 'humidity_min': 80, 'humidity_max': 95, 'shelf_life_hours': 72},
            {'name': 'Dairy Products', 'description': 'Milk, cheese, yogurt', 'temp_min': 1, 'temp_max': 4, 'humidity_min': 75, 'humidity_max': 85, 'shelf_life_hours': 168},
            {'name': 'Fresh Vegetables', 'description': 'Fresh vegetables and greens', 'temp_min': 0, 'temp_max': 8, 'humidity_min': 85, 'humidity_max': 95, 'shelf_life_hours': 120},
            {'name': 'Frozen Foods', 'description': 'Frozen food products', 'temp_min': -25, 'temp_max': -18, 'humidity_min': 70, 'humidity_max': 90, 'shelf_life_hours': 8760},
            {'name': 'Fruits', 'description': 'Fresh fruits', 'temp_min': 2, 'temp_max': 10, 'humidity_min': 80, 'humidity_max': 90, 'shelf_life_hours': 240}
        ]

        return render_template('settings.html',
                             settings=settings,
                             sensors=sensors,
                             product_types=product_types)

    except Exception as e:
        logger.error(f"Settings page error: {e}")
        flash(f"Error loading settings: {e}", 'error')
        return render_template('settings.html',
                             settings={}, sensors=[], product_types=[])

@app.route('/settings/update', methods=['POST'])
def update_settings():
    """Update system settings using backend"""
    try:
        # Update each setting from the form
        for key, value in request.form.items():
            if key.startswith('setting_'):
                setting_key = key.replace('setting_', '')
                success = backend.update_system_setting(setting_key, value, 'web_user')
                if not success:
                    flash(f'Failed to update setting: {setting_key}', 'error')

        flash('Settings updated successfully!', 'success')
        return redirect(url_for('settings_page'))
    except Exception as e:
        logger.error(f"Settings update error: {e}")
        flash(f'Error updating settings: {e}', 'error')
        return redirect(url_for('settings_page'))

@app.route('/settings/sensor/add', methods=['POST'])
def add_sensor():
    """Add new sensor configuration using backend"""
    try:
        sensor_config = {
            'device_id': request.form['device_id'],
            'location': request.form['location'],
            'product_type': request.form.get('product_type'),
            'temp_min': float(request.form['temp_min']),
            'temp_max': float(request.form['temp_max']),
            'humidity_min': float(request.form['humidity_min']),
            'humidity_max': float(request.form['humidity_max']),
            'alert_email': request.form.get('alert_email'),
            'is_active': 'is_active' in request.form
        }

        success = backend.add_sensor(sensor_config)

        if success:
            flash(f'Sensor {sensor_config["device_id"]} added successfully!', 'success')
        else:
            flash('Failed to add sensor configuration', 'error')

        return redirect(url_for('settings_page'))
    except Exception as e:
        logger.error(f"Add sensor error: {e}")
        flash(f'Error adding sensor: {e}', 'error')
        return redirect(url_for('settings_page'))

# ============================================================================
# REPORTS ROUTES WITH BACKEND INTEGRATION
# ============================================================================

@app.route('/reports')
def reports_page():
    """Enhanced reports page with backend analytics"""
    try:
        # Get comprehensive reports data from backend
        reports_data = backend.get_reports_data(hours=24)

        # Get sensor statistics for different time periods
        stats_24h = backend.reports.get_system_overview(hours=24)
        stats_7d = backend.reports.get_system_overview(hours=168)
        stats_30d = backend.reports.get_system_overview(hours=720)

        # Get sensor configurations
        sensors = backend.settings.get_sensor_config()

        # Get recent data for preview (simplified)
        recent_data = []

        return render_template('reports.html',
                             stats_24h=stats_24h,
                             stats_7d=stats_7d,
                             stats_30d=stats_30d,
                             sensors=sensors,
                             recent_data=recent_data,
                             performance_report=reports_data.get('performance_report', {}))

    except Exception as e:
        logger.error(f"Reports page error: {e}")
        flash(f"Error loading reports: {e}", 'error')
        return render_template('reports.html',
                             stats_24h={}, stats_7d={}, stats_30d={},
                             sensors=[], recent_data=[], performance_report={})

@app.route('/api/export/data')
def export_data_api():
    """Enhanced data export with backend"""
    try:
        device_id = request.args.get('device_id')
        hours = request.args.get('hours', 24, type=int)
        format_type = request.args.get('format', 'csv')

        # Export data using backend
        data = backend.export_data(device_id, hours, format_type)

        if not data:
            return jsonify({'error': 'No data available for export'}), 404

        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        device_part = f"_{device_id}" if device_id else "_all"
        filename = f'sensor_data{device_part}_{hours}h_{timestamp}.{format_type}'

        # Return appropriate response
        if format_type == 'csv':
            return Response(
                data,
                mimetype='text/csv',
                headers={'Content-Disposition': f'attachment; filename={filename}'}
            )
        else:
            return Response(
                data,
                mimetype='application/json',
                headers={'Content-Disposition': f'attachment; filename={filename}'}
            )

    except Exception as e:
        logger.error(f"Data export error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/reports/performance')
def performance_report_api():
    """API endpoint for performance reports"""
    try:
        hours = request.args.get('hours', 24, type=int)
        device_id = request.args.get('device_id')

        report = backend.reports.generate_performance_report(device_id, hours)
        return jsonify(report)

    except Exception as e:
        logger.error(f"Performance report error: {e}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# SYSTEM CONTROL AND MONITORING ROUTES
# ============================================================================

@app.route('/api/system/health')
def system_health_api():
    """Get system health status"""
    try:
        health = backend.get_system_health()
        return jsonify(health)
    except Exception as e:
        logger.error(f"System health error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/system/cleanup', methods=['POST'])
def system_cleanup_api():
    """Clean up old data"""
    try:
        days = request.args.get('days', 30, type=int)
        result = backend.cleanup_old_data(days)
        return jsonify(result)
    except Exception as e:
        logger.error(f"System cleanup error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sensor/data', methods=['POST'])
def receive_sensor_data():
    """API endpoint to receive sensor data from external sensors"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Here you would typically insert the data into your sensor_data table
        # For now, we'll just validate and acknowledge
        required_fields = ['device_id', 'temperature', 'humidity']
        if not all(field in data for field in required_fields):
            return jsonify({'error': 'Missing required fields'}), 400

        # Check if this reading violates any thresholds and create alerts
        sensor_config = backend.settings.get_sensor_config(data['device_id'])
        if sensor_config:
            temp = data['temperature']
            humidity = data['humidity']

            # Check temperature thresholds
            if temp < sensor_config['temp_min']:
                backend.create_sensor_alert(
                    device_id=data['device_id'],
                    alert_type='TEMPERATURE_LOW',
                    severity='HIGH',
                    message=f"Temperature {temp}°C is below minimum {sensor_config['temp_min']}°C",
                    current_value=temp,
                    threshold_value=sensor_config['temp_min']
                )
            elif temp > sensor_config['temp_max']:
                backend.create_sensor_alert(
                    device_id=data['device_id'],
                    alert_type='TEMPERATURE_HIGH',
                    severity='HIGH',
                    message=f"Temperature {temp}°C is above maximum {sensor_config['temp_max']}°C",
                    current_value=temp,
                    threshold_value=sensor_config['temp_max']
                )

            # Check humidity thresholds
            if humidity < sensor_config['humidity_min']:
                backend.create_sensor_alert(
                    device_id=data['device_id'],
                    alert_type='HUMIDITY_LOW',
                    severity='MEDIUM',
                    message=f"Humidity {humidity}% is below minimum {sensor_config['humidity_min']}%",
                    current_value=humidity,
                    threshold_value=sensor_config['humidity_min']
                )
            elif humidity > sensor_config['humidity_max']:
                backend.create_sensor_alert(
                    device_id=data['device_id'],
                    alert_type='HUMIDITY_HIGH',
                    severity='MEDIUM',
                    message=f"Humidity {humidity}% is above maximum {sensor_config['humidity_max']}%",
                    current_value=humidity,
                    threshold_value=sensor_config['humidity_max']
                )

        return jsonify({'status': 'success', 'message': 'Data received and processed'}), 200

    except Exception as e:
        logger.error(f"Sensor data error: {e}")
        return jsonify({'error': str(e)}), 500

# ============================================================================
# APPLICATION STARTUP AND SHUTDOWN
# ============================================================================

@app.teardown_appcontext
def close_backend(error):
    """Clean up backend resources"""
    pass

def shutdown_handler():
    """Graceful shutdown handler"""
    logger.info("Shutting down Food Monitoring System...")
    backend.shutdown()
    logger.info("Backend services stopped")

if __name__ == '__main__':
    try:
        logger.info("🍎 Starting Food Monitoring System with Backend Integration")
        logger.info("=" * 60)
        logger.info("Backend services initialized:")
        logger.info("  ✓ Settings Manager - System and sensor configuration")
        logger.info("  ✓ Alerts Manager - Real-time alerting and notifications")
        logger.info("  ✓ Reports Manager - Analytics and data export")
        logger.info("=" * 60)
        logger.info("Web interface available at: http://localhost:5000")
        logger.info("API endpoints:")
        logger.info("  • POST /api/sensor/data - Receive sensor data")
        logger.info("  • GET /api/dashboard/data - Dashboard data")
        logger.info("  • POST /api/alerts/create - Create alerts")
        logger.info("  • GET /api/export/data - Export data")
        logger.info("  • GET /api/system/health - System health")
        logger.info("=" * 60)

        # Start the Flask application
        app.run(host='0.0.0.0', port=5000, debug=False)

    except KeyboardInterrupt:
        shutdown_handler()
    except Exception as e:
        logger.error(f"Application error: {e}")
        shutdown_handler()
    finally:
        logger.info("Food Monitoring System stopped")