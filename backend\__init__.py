#!/usr/bin/env python3
"""
Backend Package for Food Monitoring System
Provides comprehensive backend services for settings, alerts, and reports
"""

from .settings_manager import SettingsManager, SystemSettings, SensorConfig
from .alerts_manager import Al<PERSON><PERSON><PERSON>ana<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>everity, AlertType
from .reports_manager import Reports<PERSON>anager, SensorStatistics, SystemReport

__version__ = "1.0.0"
__author__ = "Food Monitoring System"

# Export main classes
__all__ = [
    'SettingsManager',
    'AlertsManager',
    'ReportsManager',
    'SystemSettings',
    'SensorConfig',
    'Alert',
    'AlertSeverity',
    'AlertType',
    'SensorStatistics',
    'SystemReport',
    'BackendManager'
]

class BackendManager:
    """
    Unified backend manager that coordinates all backend services
    """

    def __init__(self, db_path: str = "food_monitoring.db"):
        """Initialize all backend managers"""
        self.db_path = db_path

        # Initialize managers
        self.settings = SettingsManager(db_path)
        self.alerts = AlertsManager(db_path)
        self.reports = ReportsManager(db_path)

        # Start alert notification service
        self.alerts.start_notification_service()

    def get_dashboard_data(self) -> dict:
        """Get comprehensive dashboard data"""
        try:
            # Get system overview
            overview = self.reports.get_system_overview(hours=24)

            # Get active alerts
            active_alerts = self.alerts.get_alerts(acknowledged=False, limit=50)

            # Get sensor statistics
            sensor_stats = self.reports.get_sensor_statistics(hours=24)

            # Get system settings
            settings = self.settings.get_all_settings()

            return {
                'overview': overview,
                'active_alerts': active_alerts,
                'sensor_statistics': sensor_stats,
                'settings': settings,
                'timestamp': self._get_current_timestamp()
            }

        except Exception as e:
            print(f"Error getting dashboard data: {e}")
            return {}

    def create_sensor_alert(self, device_id: str, alert_type: str, severity: str,
                           message: str, current_value: float = None,
                           threshold_value: float = None) -> int:
        """Create a sensor alert with proper enum conversion"""
        try:
            # Convert string enums to proper enum types
            alert_type_enum = AlertType(alert_type)
            severity_enum = AlertSeverity(severity)

            # Get sensor config for additional context
            sensor_config = self.settings.get_sensor_config(device_id)
            location = sensor_config.get('location', '') if sensor_config else ''
            product_type = sensor_config.get('product_type', '') if sensor_config else ''

            return self.alerts.create_alert(
                device_id=device_id,
                alert_type=alert_type_enum,
                severity=severity_enum,
                message=message,
                current_value=current_value,
                threshold_value=threshold_value,
                location=location,
                product_type=product_type
            )

        except Exception as e:
            print(f"Error creating sensor alert: {e}")
            return None

    def get_reports_data(self, hours: int = 24) -> dict:
        """Get comprehensive reports data"""
        try:
            # Generate performance report
            performance_report = self.reports.generate_performance_report(hours=hours)

            # Get alert statistics
            alert_stats = self.alerts.get_alert_statistics(hours=hours)

            # Get system overview
            overview = self.reports.get_system_overview(hours=hours)

            return {
                'performance_report': performance_report,
                'alert_statistics': alert_stats,
                'system_overview': overview,
                'timestamp': self._get_current_timestamp()
            }

        except Exception as e:
            print(f"Error getting reports data: {e}")
            return {}

    def export_data(self, device_id: str = None, hours: int = 24, format: str = 'csv') -> str:
        """Export sensor data in specified format"""
        try:
            return self.reports.export_sensor_data(device_id, hours, format)
        except Exception as e:
            print(f"Error exporting data: {e}")
            return ""

    def update_system_setting(self, key: str, value, changed_by: str = "system") -> bool:
        """Update a system setting"""
        try:
            return self.settings.update_setting(key, value, changed_by)
        except Exception as e:
            print(f"Error updating setting {key}: {e}")
            return False

    def add_sensor(self, sensor_config: dict) -> bool:
        """Add a new sensor configuration"""
        try:
            config = SensorConfig(
                device_id=sensor_config['device_id'],
                location=sensor_config['location'],
                product_type=sensor_config.get('product_type', ''),
                temp_min=float(sensor_config['temp_min']),
                temp_max=float(sensor_config['temp_max']),
                humidity_min=float(sensor_config['humidity_min']),
                humidity_max=float(sensor_config['humidity_max']),
                alert_email=sensor_config.get('alert_email', ''),
                is_active=sensor_config.get('is_active', True)
            )

            return self.settings.add_sensor_config(config)

        except Exception as e:
            print(f"Error adding sensor: {e}")
            return False

    def acknowledge_alert(self, alert_id: int, acknowledged_by: str) -> bool:
        """Acknowledge an alert"""
        try:
            return self.alerts.acknowledge_alert(alert_id, acknowledged_by)
        except Exception as e:
            print(f"Error acknowledging alert {alert_id}: {e}")
            return False

    def get_trend_analysis(self, device_id: str, metric: str = 'temperature', days: int = 7) -> dict:
        """Get trend analysis for a sensor"""
        try:
            return self.reports.get_trend_analysis(device_id, metric, days)
        except Exception as e:
            print(f"Error getting trend analysis: {e}")
            return {}

    def cleanup_old_data(self, days: int = 30) -> dict:
        """Clean up old data and return cleanup summary"""
        try:
            # Clean up old alerts
            alerts_cleaned = self.alerts.cleanup_old_alerts(days)

            # TODO: Add sensor data cleanup if needed

            return {
                'alerts_cleaned': alerts_cleaned,
                'cleanup_date': self._get_current_timestamp()
            }

        except Exception as e:
            print(f"Error during cleanup: {e}")
            return {}

    def get_system_health(self) -> dict:
        """Get overall system health status"""
        try:
            # Get sensor statistics
            sensor_stats = self.reports.get_sensor_statistics(hours=24)

            # Get system overview
            overview = self.reports.get_system_overview(hours=24)

            # Calculate health metrics
            if sensor_stats:
                avg_uptime = sum(s.uptime_percentage for s in sensor_stats) / len(sensor_stats)
                total_alerts = sum(s.alert_count for s in sensor_stats)

                # Determine overall health
                if avg_uptime >= 95 and total_alerts <= 5:
                    health_status = "Excellent"
                    health_score = 95
                elif avg_uptime >= 85 and total_alerts <= 15:
                    health_status = "Good"
                    health_score = 80
                elif avg_uptime >= 70 and total_alerts <= 30:
                    health_status = "Fair"
                    health_score = 65
                else:
                    health_status = "Poor"
                    health_score = 40
            else:
                health_status = "No Data"
                health_score = 0
                avg_uptime = 0
                total_alerts = 0

            return {
                'health_status': health_status,
                'health_score': health_score,
                'avg_uptime': round(avg_uptime, 2),
                'total_alerts_24h': total_alerts,
                'total_sensors': len(sensor_stats),
                'timestamp': self._get_current_timestamp()
            }

        except Exception as e:
            print(f"Error getting system health: {e}")
            return {'health_status': 'Error', 'health_score': 0}

    def shutdown(self):
        """Shutdown all backend services"""
        try:
            self.alerts.stop_notification_service()
            print("Backend services shut down successfully")
        except Exception as e:
            print(f"Error during shutdown: {e}")

    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format"""
        from datetime import datetime
        return datetime.now().isoformat()

# Convenience function to create a backend manager instance
def create_backend_manager(db_path: str = "food_monitoring.db") -> BackendManager:
    """Create and return a BackendManager instance"""
    return BackendManager(db_path)