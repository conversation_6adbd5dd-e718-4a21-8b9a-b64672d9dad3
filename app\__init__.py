#!/usr/bin/env python3
"""
Food Monitoring System - Application Factory
Main application package with factory pattern for Flask app creation
"""

import os
import logging
from flask import Flask
from flask_cors import CORS

from config.settings import get_config
from app.utils.logging import setup_logging
from app.utils.database import init_database


def create_app(config_name=None):
    """
    Application factory pattern for creating Flask app instances
    
    Args:
        config_name (str): Configuration environment name
        
    Returns:
        Flask: Configured Flask application instance
    """
    # Create Flask app
    app = Flask(__name__, 
                template_folder='../frontend/templates',
                static_folder='../frontend/static')
    
    # Load configuration
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'development')
    
    config = get_config(config_name)
    app.config.from_object(config)
    
    # Setup logging
    setup_logging(app)
    
    # Enable CORS for API endpoints
    CORS(app, resources={r"/api/*": {"origins": "*"}})
    
    # Initialize database
    init_database(app)
    
    # Register blueprints
    register_blueprints(app)
    
    # Register error handlers
    register_error_handlers(app)
    
    # Setup application context
    setup_app_context(app)
    
    return app


def register_blueprints(app):
    """Register all application blueprints"""
    
    # API blueprints
    from app.api.v1 import api_v1_bp
    app.register_blueprint(api_v1_bp, url_prefix='/api/v1')
    
    # Web interface blueprints
    from app.web.dashboard import dashboard_bp
    from app.web.alerts import alerts_bp
    from app.web.sensors import sensors_bp
    from app.web.reports import reports_bp
    from app.web.settings import settings_bp
    from app.web.auth import auth_bp
    
    app.register_blueprint(dashboard_bp)
    app.register_blueprint(alerts_bp, url_prefix='/alerts')
    app.register_blueprint(sensors_bp, url_prefix='/sensors')
    app.register_blueprint(reports_bp, url_prefix='/reports')
    app.register_blueprint(settings_bp, url_prefix='/settings')
    app.register_blueprint(auth_bp, url_prefix='/auth')


def register_error_handlers(app):
    """Register application error handlers"""
    
    @app.errorhandler(404)
    def not_found_error(error):
        return {'error': 'Resource not found'}, 404
    
    @app.errorhandler(500)
    def internal_error(error):
        app.logger.error(f'Server Error: {error}')
        return {'error': 'Internal server error'}, 500
    
    @app.errorhandler(400)
    def bad_request_error(error):
        return {'error': 'Bad request'}, 400
    
    @app.errorhandler(401)
    def unauthorized_error(error):
        return {'error': 'Unauthorized'}, 401
    
    @app.errorhandler(403)
    def forbidden_error(error):
        return {'error': 'Forbidden'}, 403


def setup_app_context(app):
    """Setup application context and global variables"""
    
    @app.before_first_request
    def initialize_app():
        """Initialize application on first request"""
        app.logger.info("🍎 Food Monitoring System starting up...")
        app.logger.info(f"Environment: {app.config['ENV']}")
        app.logger.info(f"Debug mode: {app.config['DEBUG']}")
    
    @app.before_request
    def before_request():
        """Execute before each request"""
        pass
    
    @app.after_request
    def after_request(response):
        """Execute after each request"""
        # Add security headers
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        return response
    
    @app.teardown_appcontext
    def teardown_db(error):
        """Clean up database connections"""
        pass


# Application version
__version__ = "2.0.0"
__author__ = "Food Monitoring System Team"

# Export main factory function
__all__ = ['create_app']
