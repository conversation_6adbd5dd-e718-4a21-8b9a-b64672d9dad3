#!/usr/bin/env python3
"""
Simple demo runner for the Food Monitoring System
"""

import sys
import os
import time
import threading
from datetime import datetime

# Add the monitoring_app directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def run_demo():
    """Run a simple demo of the system"""
    print("🍎 Food Monitoring System - Demo")
    print("=" * 50)

    try:
        # Import modules
        print("Loading system components...")
        from database import DatabaseManager
        from monitoring_core import MonitoringCore
        from sensor_simulator import SensorSimulator
        from app import app

        # Initialize database
        print("Initializing database...")
        db = DatabaseManager()

        # Initialize monitoring core
        print("Starting monitoring core...")
        monitoring = MonitoringCore()

        # Add some test sensor configurations
        print("Adding test sensor configurations...")
        test_sensors = [
            {
                'device_id': 'DEMO_SENSOR_001',
                'location': 'Cold Storage Room A',
                'product_type': 'Fresh Meat',
                'temp_min': -2.0,
                'temp_max': 4.0,
                'humidity_min': 80.0,
                'humidity_max': 95.0,
                'alert_email': '<EMAIL>',
                'is_active': True
            },
            {
                'device_id': 'DEMO_SENSOR_002',
                'location': 'Vegetable Storage',
                'product_type': 'Fresh Vegetables',
                'temp_min': 0.0,
                'temp_max': 8.0,
                'humidity_min': 85.0,
                'humidity_max': 95.0,
                'alert_email': '<EMAIL>',
                'is_active': True
            }
        ]

        for sensor_config in test_sensors:
            db.upsert_sensor_config(sensor_config)
            print(f"  ✓ Added sensor {sensor_config['device_id']}")

        # Create and start sensor simulators
        print("Starting sensor simulators...")
        simulators = []

        sim1 = SensorSimulator('DEMO_SENSOR_001', 'http://localhost:5000/api/data', 'Cold Storage Room A', 'Fresh Meat')
        sim2 = SensorSimulator('DEMO_SENSOR_002', 'http://localhost:5000/api/data', 'Vegetable Storage', 'Fresh Vegetables')

        simulators.extend([sim1, sim2])

        # Generate some initial data
        print("Generating initial sensor data...")
        for i in range(5):
            for sim in simulators:
                data = sim.generate_data()
                db.insert_sensor_data(data)
                print(f"  ✓ {data['device_id']}: {data['temperature']}°C, {data['humidity']}%")

        # Start monitoring
        print("Starting monitoring system...")
        monitoring.start_monitoring()

        print("\n" + "=" * 50)
        print("🎉 DEMO SYSTEM READY!")
        print("=" * 50)
        print("📊 Web Dashboard: http://localhost:5000")
        print("📡 API Endpoint: http://localhost:5000/api/data")
        print("🔧 Sensors configured: 2 demo sensors")
        print("📈 Sample data: Generated for testing")
        print("=" * 50)
        print("Starting web server...")
        print("Press Ctrl+C to stop the demo")
        print()

        # Start Flask app
        app.run(host='0.0.0.0', port=5000, debug=False)

    except KeyboardInterrupt:
        print("\n\nStopping demo...")
        if 'monitoring' in locals():
            monitoring.stop_monitoring()
        print("Demo stopped.")

    except Exception as e:
        print(f"Demo failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    run_demo()