# Food Monitoring System - Docker Compose Configuration
# Production-ready multi-container setup

version: '3.8'

services:
  # =============================================================================
  # Application Service
  # =============================================================================
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: monitoring_app
    restart: unless-stopped
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=postgresql://monitoring:${DB_PASSWORD}@db:5432/monitoring
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    env_file:
      - ../config/.env.production
    volumes:
      - app_data:/app/data
      - app_logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    networks:
      - monitoring_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # =============================================================================
  # Database Service (PostgreSQL)
  # =============================================================================
  db:
    image: postgres:15-alpine
    container_name: monitoring_db
    restart: unless-stopped
    environment:
      - POSTGRES_DB=monitoring
      - POSTGRES_USER=monitoring
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - monitoring_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U monitoring -d monitoring"]
      interval: 10s
      timeout: 5s
      retries: 5

  # =============================================================================
  # Redis Service (Caching & Message Broker)
  # =============================================================================
  redis:
    image: redis:7-alpine
    container_name: monitoring_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - monitoring_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # =============================================================================
  # Celery Worker Service (Background Tasks)
  # =============================================================================
  celery_worker:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: monitoring_celery_worker
    restart: unless-stopped
    command: celery -A app.celery worker --loglevel=info --concurrency=2
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=postgresql://monitoring:${DB_PASSWORD}@db:5432/monitoring
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    env_file:
      - ../config/.env.production
    volumes:
      - app_data:/app/data
      - app_logs:/app/logs
    depends_on:
      - db
      - redis
    networks:
      - monitoring_network

  # =============================================================================
  # Celery Beat Service (Scheduled Tasks)
  # =============================================================================
  celery_beat:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: monitoring_celery_beat
    restart: unless-stopped
    command: celery -A app.celery beat --loglevel=info --schedule=/tmp/celerybeat-schedule
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=postgresql://monitoring:${DB_PASSWORD}@db:5432/monitoring
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    env_file:
      - ../config/.env.production
    volumes:
      - app_data:/app/data
      - app_logs:/app/logs
    depends_on:
      - db
      - redis
    networks:
      - monitoring_network

  # =============================================================================
  # Nginx Reverse Proxy
  # =============================================================================
  nginx:
    image: nginx:alpine
    container_name: monitoring_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - app_logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - monitoring_network

  # =============================================================================
  # Monitoring Services
  # =============================================================================
  
  # Prometheus (Metrics Collection)
  prometheus:
    image: prom/prometheus:latest
    container_name: monitoring_prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - monitoring_network

  # Grafana (Metrics Visualization)
  grafana:
    image: grafana/grafana:latest
    container_name: monitoring_grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
    ports:
      - "3000:3000"
    networks:
      - monitoring_network

# =============================================================================
# Networks
# =============================================================================
networks:
  monitoring_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# =============================================================================
# Volumes
# =============================================================================
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_data:
    driver: local
  app_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
