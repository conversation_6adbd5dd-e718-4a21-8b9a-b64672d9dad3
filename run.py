#!/usr/bin/env python3
"""
Food Monitoring System - Application Entry Point
Main entry point for running the restructured monitoring application
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app import create_app
from config.settings import get_config


def setup_directories():
    """Create necessary directories for the application"""
    directories = [
        'data/databases',
        'data/exports', 
        'data/backups',
        'data/uploads',
        'logs',
        'frontend/static/uploads'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)


def setup_environment():
    """Setup environment variables and configuration"""
    # Set default environment if not specified
    if not os.getenv('FLASK_ENV'):
        os.environ['FLASK_ENV'] = 'development'
    
    # Load environment-specific .env file
    env_name = os.getenv('FLASK_ENV', 'development')
    env_file = f'config/.env.{env_name}'
    
    if os.path.exists(env_file):
        from dotenv import load_dotenv
        load_dotenv(env_file)
        print(f"✅ Loaded environment configuration: {env_file}")
    else:
        print(f"⚠️ Environment file not found: {env_file}")


def print_startup_banner(app, port):
    """Print application startup banner"""
    config = app.config
    
    print("\n" + "=" * 70)
    print("🍎 FOOD MONITORING SYSTEM v2.0")
    print("=" * 70)
    print(f"Environment: {config.get('ENV', 'unknown')}")
    print(f"Debug Mode: {config.get('DEBUG', False)}")
    print(f"Database: {config.get('DATABASE_URL', 'not configured')}")
    print("=" * 70)
    print("🌐 WEB INTERFACE:")
    print(f"   • Dashboard:  http://localhost:{port}")
    print(f"   • Alerts:     http://localhost:{port}/alerts")
    print(f"   • Reports:    http://localhost:{port}/reports")
    print(f"   • Settings:   http://localhost:{port}/settings")
    print("=" * 70)
    print("📡 API ENDPOINTS:")
    print(f"   • API Base:   http://localhost:{port}/api/v1")
    print(f"   • Sensors:    http://localhost:{port}/api/v1/sensors")
    print(f"   • Alerts:     http://localhost:{port}/api/v1/alerts")
    print(f"   • Dashboard:  http://localhost:{port}/api/v1/dashboard")
    print(f"   • Reports:    http://localhost:{port}/api/v1/reports")
    print("=" * 70)
    print("🚀 QUICK START:")
    print(f"   Copy this URL to your browser: http://localhost:{port}")
    print("   Press Ctrl+C to stop the server")
    print("=" * 70)


def find_available_port(start_port=5000, max_attempts=10):
    """Find an available port starting from start_port"""
    import socket
    
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    raise RuntimeError(f"Could not find available port in range {start_port}-{start_port + max_attempts}")


def run_development_server(app, host='127.0.0.1', port=None, debug=None):
    """Run the development server"""
    if port is None:
        port = find_available_port()
    
    if debug is None:
        debug = app.config.get('DEBUG', False)
    
    print_startup_banner(app, port)
    
    try:
        app.run(
            host=host,
            port=port,
            debug=debug,
            use_reloader=debug,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")


def run_production_server(app, host='0.0.0.0', port=8000):
    """Run the production server using Gunicorn"""
    try:
        import gunicorn.app.wsgiapp as wsgi
        
        # Gunicorn configuration
        sys.argv = [
            'gunicorn',
            '--bind', f'{host}:{port}',
            '--workers', '4',
            '--worker-class', 'sync',
            '--timeout', '120',
            '--keepalive', '5',
            '--max-requests', '1000',
            '--max-requests-jitter', '100',
            '--access-logfile', 'logs/access.log',
            '--error-logfile', 'logs/error.log',
            '--log-level', 'info',
            'wsgi:application'
        ]
        
        print_startup_banner(app, port)
        wsgi.run()
        
    except ImportError:
        print("❌ Gunicorn not installed. Install with: pip install gunicorn")
        print("   Falling back to development server...")
        run_development_server(app, host, port, debug=False)


def main():
    """Main application entry point"""
    parser = argparse.ArgumentParser(description='Food Monitoring System')
    parser.add_argument('--env', choices=['development', 'testing', 'production'], 
                       help='Environment to run in')
    parser.add_argument('--host', default='127.0.0.1', 
                       help='Host to bind to (default: 127.0.0.1)')
    parser.add_argument('--port', type=int, 
                       help='Port to bind to (default: auto-detect)')
    parser.add_argument('--debug', action='store_true', 
                       help='Enable debug mode')
    parser.add_argument('--production', action='store_true',
                       help='Run in production mode with Gunicorn')
    parser.add_argument('--init-db', action='store_true',
                       help='Initialize database and exit')
    parser.add_argument('--seed-data', action='store_true',
                       help='Seed database with sample data')
    
    args = parser.parse_args()
    
    # Set environment
    if args.env:
        os.environ['FLASK_ENV'] = args.env
    
    # Setup environment and directories
    setup_environment()
    setup_directories()
    
    # Create application
    app = create_app()
    
    # Handle special commands
    if args.init_db:
        print("🔧 Initializing database...")
        with app.app_context():
            from scripts.init_db import initialize_database
            initialize_database()
        print("✅ Database initialized successfully")
        return
    
    if args.seed_data:
        print("🌱 Seeding database with sample data...")
        with app.app_context():
            from scripts.seed_data import seed_sample_data
            seed_sample_data()
        print("✅ Sample data seeded successfully")
        return
    
    # Run server
    if args.production or os.getenv('FLASK_ENV') == 'production':
        run_production_server(app, args.host, args.port or 8000)
    else:
        run_development_server(app, args.host, args.port, args.debug)


if __name__ == '__main__':
    main()
