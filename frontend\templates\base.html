<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Food Monitoring System - Real-time temperature and humidity monitoring for food safety">
    <meta name="author" content="Food Monitoring System Team">
    
    <title>{% block title %}Food Monitoring System{% endblock %}</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Main Stylesheet -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    
    <!-- Page-specific CSS -->
    {% block extra_css %}{% endblock %}
    
    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS Variables for Dynamic Theming -->
    <style>
        :root {
            --current-time: "{{ current_time or '' }}";
            --app-version: "v2.0.0";
        }
    </style>
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-color text-white px-4 py-2 rounded">
        Skip to main content
    </a>

    <!-- Header -->
    <header class="header" role="banner">
        <div class="container">
            <div class="header-content">
                <h1>
                    <i class="fas fa-thermometer-half" aria-hidden="true"></i>
                    {% block header_title %}Food Monitoring System{% endblock %}
                </h1>
                <p>{% block header_subtitle %}Real-time monitoring for food safety and quality assurance{% endblock %}</p>
                
                <!-- Navigation -->
                <nav class="nav" role="navigation" aria-label="Main navigation">
                    <ul class="nav-list">
                        <li>
                            <a href="{{ url_for('dashboard.index') }}" 
                               class="nav-link {{ 'active' if request.endpoint == 'dashboard.index' else '' }}">
                                <i class="fas fa-tachometer-alt" aria-hidden="true"></i>
                                Dashboard
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('alerts.index') }}" 
                               class="nav-link {{ 'active' if request.endpoint.startswith('alerts') else '' }}">
                                <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
                                Alerts
                                {% if active_alerts_count and active_alerts_count > 0 %}
                                    <span class="badge badge-error">{{ active_alerts_count }}</span>
                                {% endif %}
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('sensors.index') }}" 
                               class="nav-link {{ 'active' if request.endpoint.startswith('sensors') else '' }}">
                                <i class="fas fa-microchip" aria-hidden="true"></i>
                                Sensors
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('reports.index') }}" 
                               class="nav-link {{ 'active' if request.endpoint.startswith('reports') else '' }}">
                                <i class="fas fa-chart-line" aria-hidden="true"></i>
                                Reports
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('settings.index') }}" 
                               class="nav-link {{ 'active' if request.endpoint.startswith('settings') else '' }}">
                                <i class="fas fa-cog" aria-hidden="true"></i>
                                Settings
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages" role="alert" aria-live="polite">
                <div class="container">
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} flash-message">
                            <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-circle' if category == 'error' else 'info-circle' }}" aria-hidden="true"></i>
                            <span>{{ message }}</span>
                            <button type="button" class="btn-close" onclick="this.parentElement.remove()" aria-label="Close">
                                <i class="fas fa-times" aria-hidden="true"></i>
                            </button>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main id="main-content" class="main" role="main">
        <div class="container">
            <!-- Page Header -->
            {% block page_header %}{% endblock %}
            
            <!-- Page Content -->
            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Food Monitoring System</h3>
                    <p>Ensuring food safety through intelligent monitoring</p>
                </div>
                
                <div class="footer-section">
                    <h4>System Status</h4>
                    <div class="system-status">
                        <span class="status-indicator status-{{ system_health.health_status.lower() if system_health else 'unknown' }}"></span>
                        <span>{{ system_health.health_status if system_health else 'Unknown' }}</span>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="{{ url_for('api_v1.system_health') }}">API Status</a></li>
                        <li><a href="/docs">Documentation</a></li>
                        <li><a href="/support">Support</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Last Updated</h4>
                    <time datetime="{{ current_time }}">
                        {{ current_time or 'Unknown' }}
                    </time>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Food Monitoring System. All rights reserved.</p>
                <p>Version {{ config.get('APP_VERSION', 'v2.0.0') }}</p>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Core JavaScript -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    <!-- Page-specific JavaScript -->
    {% block extra_js %}{% endblock %}

    <!-- Real-time Updates -->
    <script>
        // Auto-refresh functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-refresh every 30 seconds for dashboard
            if (window.location.pathname === '/' || window.location.pathname.includes('dashboard')) {
                setInterval(function() {
                    if (!document.hidden) {
                        window.location.reload();
                    }
                }, 30000);
            }
            
            // Remove flash messages after 5 seconds
            setTimeout(function() {
                const flashMessages = document.querySelectorAll('.flash-message');
                flashMessages.forEach(function(message) {
                    message.style.opacity = '0';
                    setTimeout(function() {
                        message.remove();
                    }, 300);
                });
            }, 5000);
        });
    </script>

    <!-- Analytics and Monitoring (if enabled) -->
    {% if config.get('ANALYTICS_ENABLED') %}
        <!-- Add analytics code here -->
    {% endif %}
</body>
</html>
