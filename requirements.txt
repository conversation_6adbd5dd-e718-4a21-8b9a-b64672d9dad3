# Food Monitoring System - Core Dependencies
# Production-ready requirements for the monitoring application

# =============================================================================
# Web Framework
# =============================================================================
Flask==3.0.0
Werkzeug==3.0.1
Jinja2==3.1.2
MarkupSafe==2.1.3
itsdangerous==2.1.2
click==8.1.7
blinker==1.7.0

# =============================================================================
# Database & ORM
# =============================================================================
SQLAlchemy==2.0.23
Flask-SQLAlchemy==3.1.1
alembic==1.13.1
psycopg2-binary==2.9.9

# =============================================================================
# Data Validation & Serialization
# =============================================================================
marshmallow==3.20.1
Flask-Marshmallow==0.15.0
marshmallow-sqlalchemy==0.29.0

# =============================================================================
# Authentication & Security
# =============================================================================
Flask-JWT-Extended==4.6.0
bcrypt==4.1.2
cryptography==41.0.8

# =============================================================================
# API & CORS
# =============================================================================
Flask-CORS==4.0.0
Flask-RESTful==0.3.10

# =============================================================================
# Caching & Background Tasks
# =============================================================================
redis==5.0.1
celery==5.3.4
Flask-Caching==2.1.0

# =============================================================================
# Configuration & Environment
# =============================================================================
python-dotenv==1.0.0
pydantic==2.5.2
pydantic-settings==2.1.0

# =============================================================================
# HTTP Requests
# =============================================================================
requests==2.31.0
urllib3==2.1.0

# =============================================================================
# Date & Time
# =============================================================================
python-dateutil==2.8.2
pytz==2023.3

# =============================================================================
# Utilities
# =============================================================================
typing-extensions==4.8.0
email-validator==2.1.0
phonenumbers==8.13.27

# =============================================================================
# Monitoring & Logging
# =============================================================================
prometheus-client==0.19.0
structlog==23.2.0

# =============================================================================
# File Processing
# =============================================================================
openpyxl==3.1.2
pandas==2.1.4