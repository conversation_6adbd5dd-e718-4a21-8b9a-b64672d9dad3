#!/usr/bin/env python3
"""
Dashboard Web Controller
Handles web interface for the main dashboard
"""

import logging
from datetime import datetime
from flask import Blueprint, render_template, request, flash, current_app

from app.core.services.sensor_service import SensorService
from app.core.services.alert_service import AlertService
from app.core.services.report_service import ReportService
from app.core.repositories.sensor_repository import SensorRepository
from app.core.repositories.alert_repository import AlertRepository
from app.models.enums import AlertStatus


# Create blueprint
dashboard_bp = Blueprint('dashboard', __name__)

# Initialize logger
logger = logging.getLogger(__name__)


def get_services():
    """Get service instances with dependency injection"""
    # In a real application, this would use a DI container
    sensor_repo = SensorRepository()
    alert_repo = AlertRepository()
    
    sensor_service = SensorService(sensor_repo, alert_repo)
    alert_service = AlertService(alert_repo, sensor_repo)
    report_service = ReportService(sensor_repo, alert_repo)
    
    return sensor_service, alert_service, report_service


@dashboard_bp.route('/')
def index():
    """
    Main dashboard page
    Displays system overview, active alerts, and sensor statistics
    """
    try:
        # Get service instances
        sensor_service, alert_service, report_service = get_services()
        
        # Get dashboard data
        hours = request.args.get('hours', 24, type=int)
        
        # System overview
        overview = report_service.get_system_overview(hours=hours)
        
        # Active alerts
        active_alerts = alert_service.get_alerts(
            status=AlertStatus.ACTIVE,
            limit=10
        )
        
        # Sensor statistics
        sensor_statistics = sensor_service.get_sensor_statistics(hours=hours)
        
        # System health
        system_health = report_service.get_system_health()
        
        # Recent sensor data for charts
        recent_data = []
        all_sensors = sensor_service.get_all_sensors()
        for sensor in all_sensors[:5]:  # Limit to 5 sensors for performance
            data = sensor_service.get_sensor_data(sensor['device_id'], hours=6)
            if data:
                recent_data.append({
                    'device_id': sensor['device_id'],
                    'location': sensor['location'],
                    'data': data[-20:]  # Last 20 readings
                })
        
        # Alert statistics
        alert_stats = alert_service.get_alert_statistics(hours=hours)
        
        # Prepare template context
        context = {
            'overview': overview,
            'active_alerts': active_alerts,
            'sensor_statistics': sensor_statistics,
            'system_health': system_health,
            'recent_data': recent_data,
            'alert_statistics': alert_stats,
            'current_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'hours_filter': hours,
            'active_alerts_count': len(active_alerts)
        }
        
        return render_template('pages/dashboard.html', **context)
        
    except Exception as e:
        logger.error(f"Dashboard error: {e}")
        flash(f"Error loading dashboard: {str(e)}", 'error')
        
        # Return minimal dashboard with error state
        return render_template('pages/dashboard.html', 
                             overview={}, 
                             active_alerts=[], 
                             sensor_statistics=[],
                             system_health={'health_status': 'Error'},
                             recent_data=[],
                             alert_statistics={},
                             current_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                             hours_filter=24,
                             active_alerts_count=0)


@dashboard_bp.route('/refresh')
def refresh():
    """
    AJAX endpoint for refreshing dashboard data
    Returns JSON data for dynamic updates
    """
    try:
        # Get service instances
        sensor_service, alert_service, report_service = get_services()
        
        hours = request.args.get('hours', 24, type=int)
        
        # Get updated data
        overview = report_service.get_system_overview(hours=hours)
        active_alerts = alert_service.get_alerts(status=AlertStatus.ACTIVE, limit=10)
        system_health = report_service.get_system_health()
        alert_stats = alert_service.get_alert_statistics(hours=hours)
        
        # Return JSON response
        return {
            'status': 'success',
            'data': {
                'overview': overview,
                'active_alerts': active_alerts,
                'system_health': system_health,
                'alert_statistics': alert_stats,
                'timestamp': datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Dashboard refresh error: {e}")
        return {
            'status': 'error',
            'message': str(e)
        }, 500


@dashboard_bp.route('/widget/<widget_type>')
def widget(widget_type):
    """
    Individual widget endpoint for modular dashboard updates
    
    Args:
        widget_type: Type of widget (overview, alerts, sensors, health)
    """
    try:
        # Get service instances
        sensor_service, alert_service, report_service = get_services()
        
        hours = request.args.get('hours', 24, type=int)
        
        if widget_type == 'overview':
            data = report_service.get_system_overview(hours=hours)
        elif widget_type == 'alerts':
            data = alert_service.get_alerts(status=AlertStatus.ACTIVE, limit=10)
        elif widget_type == 'sensors':
            data = sensor_service.get_sensor_statistics(hours=hours)
        elif widget_type == 'health':
            data = report_service.get_system_health()
        else:
            return {'error': 'Invalid widget type'}, 400
        
        return {
            'status': 'success',
            'widget_type': widget_type,
            'data': data,
            'timestamp': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Widget {widget_type} error: {e}")
        return {
            'status': 'error',
            'widget_type': widget_type,
            'message': str(e)
        }, 500


@dashboard_bp.route('/export')
def export_dashboard():
    """
    Export dashboard data in various formats
    """
    try:
        # Get service instances
        sensor_service, alert_service, report_service = get_services()
        
        format_type = request.args.get('format', 'json')
        hours = request.args.get('hours', 24, type=int)
        
        # Collect all dashboard data
        dashboard_data = {
            'overview': report_service.get_system_overview(hours=hours),
            'active_alerts': alert_service.get_alerts(status=AlertStatus.ACTIVE),
            'sensor_statistics': sensor_service.get_sensor_statistics(hours=hours),
            'system_health': report_service.get_system_health(),
            'alert_statistics': alert_service.get_alert_statistics(hours=hours),
            'export_timestamp': datetime.now().isoformat(),
            'time_period_hours': hours
        }
        
        if format_type == 'json':
            from flask import jsonify
            return jsonify(dashboard_data)
        
        elif format_type == 'csv':
            # Convert to CSV format
            import csv
            import io
            from flask import Response
            
            output = io.StringIO()
            writer = csv.writer(output)
            
            # Write overview data
            writer.writerow(['Metric', 'Value'])
            for key, value in dashboard_data['overview'].items():
                writer.writerow([key, value])
            
            # Write alert data
            writer.writerow([])
            writer.writerow(['Active Alerts'])
            writer.writerow(['ID', 'Device', 'Type', 'Severity', 'Message', 'Created'])
            for alert in dashboard_data['active_alerts']:
                writer.writerow([
                    alert.get('id', ''),
                    alert.get('device_id', ''),
                    alert.get('alert_type', ''),
                    alert.get('severity', ''),
                    alert.get('message', ''),
                    alert.get('created_at', '')
                ])
            
            response = Response(
                output.getvalue(),
                mimetype='text/csv',
                headers={'Content-Disposition': f'attachment; filename=dashboard_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'}
            )
            return response
        
        else:
            return {'error': 'Unsupported format'}, 400
            
    except Exception as e:
        logger.error(f"Dashboard export error: {e}")
        return {'error': str(e)}, 500


@dashboard_bp.errorhandler(404)
def not_found(error):
    """Handle 404 errors for dashboard routes"""
    return render_template('errors/404.html'), 404


@dashboard_bp.errorhandler(500)
def internal_error(error):
    """Handle 500 errors for dashboard routes"""
    logger.error(f"Dashboard internal error: {error}")
    return render_template('errors/500.html'), 500


# Context processor to inject common dashboard data
@dashboard_bp.app_context_processor
def inject_dashboard_context():
    """Inject common dashboard context variables"""
    try:
        # Get service instances
        _, alert_service, _ = get_services()
        
        # Get active alerts count for navigation badge
        active_alerts = alert_service.get_alerts(status=AlertStatus.ACTIVE, limit=1)
        active_alerts_count = len(active_alerts)
        
        return {
            'active_alerts_count': active_alerts_count
        }
    except Exception as e:
        logger.error(f"Context processor error: {e}")
        return {
            'active_alerts_count': 0
        }
