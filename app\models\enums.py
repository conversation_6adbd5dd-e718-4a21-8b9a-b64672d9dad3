#!/usr/bin/env python3
"""
Enumerations
Defines all enums used throughout the monitoring system
"""

from enum import Enum, auto


class AlertType(Enum):
    """Alert type enumeration"""
    TEMPERATURE_HIGH = "TEMPERATURE_HIGH"
    TEMPERATURE_LOW = "TEMPERATURE_LOW"
    HUMIDITY_HIGH = "HUMIDITY_HIGH"
    HUMIDITY_LOW = "HUMIDITY_LOW"
    SENSOR_OFFLINE = "SENSOR_OFFLINE"
    SENSOR_ERROR = "SENSOR_ERROR"
    SYSTEM_ERROR = "SYSTEM_ERROR"
    MAINTENANCE_REQUIRED = "MAINTENANCE_REQUIRED"
    BATTERY_LOW = "BATTERY_LOW"
    COMMUNICATION_ERROR = "COMMUNICATION_ERROR"


class AlertSeverity(Enum):
    """Alert severity enumeration"""
    CRITICAL = "CRITICAL"
    HIGH = "HIGH"
    MEDIUM = "MEDIUM"
    LOW = "LOW"
    INFO = "INFO"


class AlertStatus(Enum):
    """Alert status enumeration"""
    ACTIVE = "ACTIVE"
    ACKNOWLEDGED = "ACKNOWLEDGED"
    RESOLVED = "RESOLVED"
    CLOSED = "CLOSED"
    SUPPRESSED = "SUPPRESSED"


class SensorStatus(Enum):
    """Sensor status enumeration"""
    ONLINE = "ONLINE"
    OFFLINE = "OFFLINE"
    ERROR = "ERROR"
    MAINTENANCE = "MAINTENANCE"
    UNKNOWN = "UNKNOWN"


class ProductType(Enum):
    """Product type enumeration for monitoring profiles"""
    FRESH_MEAT = "FRESH_MEAT"
    DAIRY_PRODUCTS = "DAIRY_PRODUCTS"
    FRESH_VEGETABLES = "FRESH_VEGETABLES"
    FROZEN_FOODS = "FROZEN_FOODS"
    FRUITS = "FRUITS"
    SEAFOOD = "SEAFOOD"
    BAKERY = "BAKERY"
    BEVERAGES = "BEVERAGES"
    CUSTOM = "CUSTOM"


class NotificationChannel(Enum):
    """Notification channel enumeration"""
    EMAIL = "EMAIL"
    SMS = "SMS"
    WEBHOOK = "WEBHOOK"
    SLACK = "SLACK"
    TEAMS = "TEAMS"
    PUSH = "PUSH"


class UserRole(Enum):
    """User role enumeration"""
    ADMIN = "ADMIN"
    MANAGER = "MANAGER"
    OPERATOR = "OPERATOR"
    VIEWER = "VIEWER"


class DataExportFormat(Enum):
    """Data export format enumeration"""
    CSV = "CSV"
    JSON = "JSON"
    EXCEL = "EXCEL"
    PDF = "PDF"


class SystemHealthStatus(Enum):
    """System health status enumeration"""
    EXCELLENT = "EXCELLENT"
    GOOD = "GOOD"
    FAIR = "FAIR"
    POOR = "POOR"
    CRITICAL = "CRITICAL"
    UNKNOWN = "UNKNOWN"


class LogLevel(Enum):
    """Log level enumeration"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


# Utility functions for enum operations
def get_enum_values(enum_class):
    """Get all values from an enum class"""
    return [item.value for item in enum_class]


def get_enum_choices(enum_class):
    """Get enum choices for form fields"""
    return [(item.value, item.value.replace('_', ' ').title()) for item in enum_class]


def is_valid_enum_value(enum_class, value):
    """Check if a value is valid for an enum"""
    try:
        enum_class(value)
        return True
    except ValueError:
        return False


# Export utility functions
__all__ = [
    'AlertType',
    'AlertSeverity',
    'AlertStatus',
    'SensorStatus',
    'ProductType',
    'NotificationChannel',
    'UserRole',
    'DataExportFormat',
    'SystemHealthStatus',
    'LogLevel',
    'get_enum_values',
    'get_enum_choices',
    'is_valid_enum_value'
]
