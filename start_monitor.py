#!/usr/bin/env python3
"""
Food Monitoring System - Startup Script
Handles initialization and starts the monitoring application
"""

import os
import sys
import subprocess
import time
import sqlite3
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = ['flask', 'sqlite3']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - OK")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - Missing")
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install flask")
        return False
    
    return True

def setup_directories():
    """Create necessary directories"""
    print("📁 Setting up directories...")
    
    directories = [
        'data',
        'data/databases',
        'data/exports',
        'data/backups',
        'logs',
        'static/uploads'
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ Created directory: {directory}")
        except Exception as e:
            print(f"⚠️ Could not create directory {directory}: {e}")

def initialize_database():
    """Initialize the database if it doesn't exist"""
    print("🗄️ Initializing database...")
    
    db_path = "food_monitoring.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create basic tables if they don't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sensor_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                device_id TEXT NOT NULL,
                temperature REAL NOT NULL,
                humidity REAL NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                location TEXT,
                product_type TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                device_id TEXT NOT NULL,
                alert_type TEXT NOT NULL,
                severity TEXT NOT NULL,
                message TEXT NOT NULL,
                current_value REAL,
                threshold_value REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                acknowledged BOOLEAN DEFAULT 0
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sensors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                device_id TEXT UNIQUE NOT NULL,
                location TEXT NOT NULL,
                product_type TEXT,
                temp_min REAL DEFAULT -2.0,
                temp_max REAL DEFAULT 4.0,
                humidity_min REAL DEFAULT 80.0,
                humidity_max REAL DEFAULT 95.0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Insert sample sensor configurations
        sample_sensors = [
            ('SENSOR_001', 'Cold Storage A', 'FRESH_MEAT', -2.0, 4.0, 80.0, 95.0),
            ('SENSOR_002', 'Dairy Refrigerator', 'DAIRY_PRODUCTS', 1.0, 4.0, 75.0, 85.0),
            ('SENSOR_003', 'Freezer Unit', 'FROZEN_FOODS', -25.0, -18.0, 70.0, 90.0)
        ]
        
        for sensor in sample_sensors:
            cursor.execute('''
                INSERT OR IGNORE INTO sensors 
                (device_id, location, product_type, temp_min, temp_max, humidity_min, humidity_max)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', sensor)
        
        conn.commit()
        conn.close()
        
        print("✅ Database initialized successfully")
        return True
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

def check_port_availability(port=5000):
    """Check if the port is available"""
    import socket
    
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            return True
    except OSError:
        return False

def start_application():
    """Start the monitoring application"""
    print("🚀 Starting Food Monitoring System...")
    
    # Check if port is available
    if not check_port_availability():
        print("⚠️ Port 5000 is already in use")
        print("The application will try to find an available port")
    
    try:
        # Start the application
        subprocess.run([sys.executable, 'app_monitor.py'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start application: {e}")
        return False
    except KeyboardInterrupt:
        print("\n🛑 Application stopped by user")
        return True
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Main startup function"""
    print("🍎 Food Monitoring System - Startup")
    print("=" * 50)
    
    # Check if app_monitor.py exists
    if not os.path.exists('app_monitor.py'):
        print("❌ app_monitor.py not found in current directory")
        print("Make sure you're running this script from the correct directory")
        return
    
    # Step 1: Check dependencies
    if not check_dependencies():
        print("\n❌ Dependency check failed")
        print("Please install required packages and try again")
        return
    
    print()
    
    # Step 2: Setup directories
    setup_directories()
    print()
    
    # Step 3: Initialize database
    if not initialize_database():
        print("\n❌ Database initialization failed")
        return
    
    print()
    
    # Step 4: Start application
    print("🎯 All checks passed! Starting application...")
    print("=" * 50)
    start_application()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Startup interrupted by user")
    except Exception as e:
        print(f"\n❌ Startup error: {e}")
        print("Please check the error and try again")
