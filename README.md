# 🍎 Food Monitoring System

A comprehensive temperature and humidity monitoring system for perishable goods in the food processing industry. This system ensures product quality and minimizes spoilage during transportation and storage.

## 🌟 Features

### Core Monitoring
- **Real-time sensor data collection** - Continuous monitoring of temperature and humidity
- **Threshold-based alerting** - Automatic alerts when conditions exceed safe limits
- **Multi-location support** - Monitor multiple storage locations simultaneously
- **Product-specific thresholds** - Different limits for meat, dairy, vegetables, fruits, and frozen foods

### Alert System
- **Email notifications** - Instant alerts via email with detailed information
- **Alert escalation** - Progressive escalation based on severity levels
- **Alert acknowledgment** - Track who acknowledged alerts and when
- **Cooldown periods** - Prevent alert spam with configurable cooldown times

### Web Dashboard
- **Real-time monitoring** - Live dashboard with auto-refresh
- **Data visualization** - Charts and graphs for trend analysis
- **Sensor management** - Configure and manage sensor settings
- **Historical reporting** - View historical data and generate reports

### Data Management
- **SQLite database** - Reliable local data storage
- **Data retention policies** - Automatic cleanup of old data
- **Export functionality** - Export data to CSV for external analysis
- **Statistical analysis** - Built-in analytics and trend detection

## 🚀 Quick Start

### 1. Installation

```bash
# Clone or download the project
cd monitoring_app

# Install dependencies
pip install -r requirements.txt
```

### 2. Initialize the System

```bash
# Initialize with default sensor configurations
python main.py --init
```

### 3. Start the System

```bash
# Start with sensor simulation for testing
python main.py --simulate

# Or start without simulation (for real sensors)
python main.py
```

### 4. Access the Dashboard

Open your web browser and navigate to:
- **Dashboard**: http://localhost:5000
- **API Endpoint**: http://localhost:5000/api/data

## 📊 Default Sensor Configurations

The system comes pre-configured with sensors for different product types:

| Sensor ID | Location | Product Type | Temp Range | Humidity Range |
|-----------|----------|--------------|------------|----------------|
| SENSOR_001 | Cold Storage Room A | Fresh Meat | -2°C to 4°C | 80% to 95% |
| SENSOR_002 | Dairy Refrigerator | Dairy Products | 1°C to 4°C | 75% to 85% |
| SENSOR_003 | Vegetable Storage | Fresh Vegetables | 0°C to 8°C | 85% to 95% |
| SENSOR_004 | Freezer Unit 1 | Frozen Foods | -25°C to -18°C | 70% to 90% |
| SENSOR_005 | Fruit Storage | Fruits | 2°C to 10°C | 80% to 90% |

## 🔧 Configuration

### Email Alerts Setup

Edit `monitoring_app/config.json`:

```json
{
  "email": {
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "your-app-password",
    "from_email": "<EMAIL>"
  }
}
```

### Adding Real Sensors

Send HTTP POST requests to `/api/data` with JSON payload:

```json
{
  "device_id": "SENSOR_001",
  "temperature": 3.5,
  "humidity": 85.2,
  "location": "Cold Storage Room A",
  "product_type": "Fresh Meat",
  "batch_id": "BATCH_20250723_001",
  "timestamp": "2025-07-23T15:30:00"
}
```

## 📱 API Endpoints

### Data Collection
- `POST /api/data` - Submit sensor readings
- `GET /api/sensor/<device_id>/data` - Get sensor data
- `GET /api/sensors` - List all sensors

### Alert Management
- `GET /api/alerts` - Get active alerts
- `POST /api/alert/<id>/acknowledge` - Acknowledge alert

### System Control
- `POST /api/system/start` - Start monitoring
- `POST /api/system/stop` - Stop monitoring
- `GET /api/system/status` - Get system status

## 🎯 Usage Examples

### Starting with Simulation
```bash
python main.py --init --simulate --debug
```

### Production Deployment
```bash
python main.py --host 0.0.0.0 --port 8080
```

### Sensor Simulation Only
```bash
python sensor_simulator.py
```