#!/usr/bin/env python3
"""
Base Repository
Abstract base class for all repositories with common functionality
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from datetime import datetime

from app.utils.database import get_db_connection


class BaseRepository(ABC):
    """
    Abstract base repository class
    Provides common database operations and patterns
    """
    
    def __init__(self, db_connection=None):
        """
        Initialize repository with database connection
        
        Args:
            db_connection: Database connection (optional, will create if not provided)
        """
        self.db = db_connection or get_db_connection()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def execute_query(self, query: str, params: tuple = None) -> Any:
        """
        Execute a database query
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            Query result
        """
        try:
            cursor = self.db.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            return cursor
        except Exception as e:
            self.logger.error(f"Database query error: {e}")
            raise
    
    def execute_insert(self, query: str, params: tuple = None) -> Optional[int]:
        """
        Execute an insert query and return the last row ID
        
        Args:
            query: SQL insert query
            params: Query parameters
            
        Returns:
            Last inserted row ID or None if failed
        """
        try:
            cursor = self.execute_query(query, params)
            self.db.commit()
            return cursor.lastrowid
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Insert query error: {e}")
            return None
    
    def execute_update(self, query: str, params: tuple = None) -> bool:
        """
        Execute an update query
        
        Args:
            query: SQL update query
            params: Query parameters
            
        Returns:
            True if successful, False otherwise
        """
        try:
            cursor = self.execute_query(query, params)
            self.db.commit()
            return cursor.rowcount > 0
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Update query error: {e}")
            return False
    
    def execute_delete(self, query: str, params: tuple = None) -> int:
        """
        Execute a delete query
        
        Args:
            query: SQL delete query
            params: Query parameters
            
        Returns:
            Number of rows deleted
        """
        try:
            cursor = self.execute_query(query, params)
            self.db.commit()
            return cursor.rowcount
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Delete query error: {e}")
            return 0
    
    def fetch_one(self, query: str, params: tuple = None) -> Optional[Dict]:
        """
        Fetch a single row from the database
        
        Args:
            query: SQL query
            params: Query parameters
            
        Returns:
            Single row as dictionary or None
        """
        try:
            cursor = self.execute_query(query, params)
            row = cursor.fetchone()
            if row:
                # Convert row to dictionary using column names
                columns = [description[0] for description in cursor.description]
                return dict(zip(columns, row))
            return None
        except Exception as e:
            self.logger.error(f"Fetch one error: {e}")
            return None
    
    def fetch_all(self, query: str, params: tuple = None) -> List[Dict]:
        """
        Fetch all rows from the database
        
        Args:
            query: SQL query
            params: Query parameters
            
        Returns:
            List of rows as dictionaries
        """
        try:
            cursor = self.execute_query(query, params)
            rows = cursor.fetchall()
            if rows:
                # Convert rows to dictionaries using column names
                columns = [description[0] for description in cursor.description]
                return [dict(zip(columns, row)) for row in rows]
            return []
        except Exception as e:
            self.logger.error(f"Fetch all error: {e}")
            return []
    
    def fetch_many(self, query: str, params: tuple = None, limit: int = 100) -> List[Dict]:
        """
        Fetch multiple rows with limit
        
        Args:
            query: SQL query
            params: Query parameters
            limit: Maximum number of rows to fetch
            
        Returns:
            List of rows as dictionaries
        """
        try:
            cursor = self.execute_query(query, params)
            rows = cursor.fetchmany(limit)
            if rows:
                # Convert rows to dictionaries using column names
                columns = [description[0] for description in cursor.description]
                return [dict(zip(columns, row)) for row in rows]
            return []
        except Exception as e:
            self.logger.error(f"Fetch many error: {e}")
            return []
    
    def table_exists(self, table_name: str) -> bool:
        """
        Check if a table exists in the database
        
        Args:
            table_name: Name of the table to check
            
        Returns:
            True if table exists, False otherwise
        """
        try:
            query = """
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            """
            result = self.fetch_one(query, (table_name,))
            return result is not None
        except Exception as e:
            self.logger.error(f"Table exists check error: {e}")
            return False
    
    def create_table_if_not_exists(self, table_name: str, schema: str) -> bool:
        """
        Create a table if it doesn't exist
        
        Args:
            table_name: Name of the table
            schema: Table schema SQL
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.table_exists(table_name):
                self.execute_query(schema)
                self.db.commit()
                self.logger.info(f"Created table: {table_name}")
            return True
        except Exception as e:
            self.logger.error(f"Create table error: {e}")
            return False
    
    def get_table_info(self, table_name: str) -> List[Dict]:
        """
        Get table schema information
        
        Args:
            table_name: Name of the table
            
        Returns:
            List of column information
        """
        try:
            query = f"PRAGMA table_info({table_name})"
            return self.fetch_all(query)
        except Exception as e:
            self.logger.error(f"Get table info error: {e}")
            return []
    
    def build_where_clause(self, filters: Dict[str, Any]) -> tuple:
        """
        Build WHERE clause from filters dictionary
        
        Args:
            filters: Dictionary of filter conditions
            
        Returns:
            Tuple of (where_clause, params)
        """
        if not filters:
            return "", ()
        
        conditions = []
        params = []
        
        for key, value in filters.items():
            if value is not None:
                if key.endswith('_after') or key.endswith('_before'):
                    # Date range filters
                    column = key.replace('_after', '').replace('_before', '')
                    operator = '>=' if key.endswith('_after') else '<='
                    conditions.append(f"{column} {operator} ?")
                    params.append(value)
                elif key.endswith('_like'):
                    # LIKE filters
                    column = key.replace('_like', '')
                    conditions.append(f"{column} LIKE ?")
                    params.append(f"%{value}%")
                else:
                    # Exact match filters
                    conditions.append(f"{key} = ?")
                    params.append(value)
        
        where_clause = " AND ".join(conditions)
        return f"WHERE {where_clause}" if where_clause else "", tuple(params)
    
    def close(self):
        """Close database connection"""
        if self.db:
            self.db.close()
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()
