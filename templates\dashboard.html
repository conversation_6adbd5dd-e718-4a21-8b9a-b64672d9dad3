{% extends "base.html" %}

{% block title %}Dashboard - Food Monitoring System{% endblock %}

{% block extra_css %}
<style>
    .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .card:hover {
        transform: translateY(-2px);
    }

    .status-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .status-item {
        text-align: center;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .status-value {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
    }

    .status-label {
        color: #666;
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .alert-high {
        background-color: #f8d7da;
        border-color: #dc3545;
        color: #721c24;
    }

    .alert-medium {
        background-color: #fff3cd;
        border-color: #ffc107;
        color: #856404;
    }

    .alert-low {
        background-color: #d1ecf1;
        border-color: #17a2b8;
        color: #0c5460;
    }

    .sensor-list {
        list-style: none;
    }

    .sensor-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        margin: 0.5rem 0;
        background: #f8f9fa;
        border-radius: 5px;
    }

    .sensor-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .refresh-info {
        text-align: center;
        color: #666;
        margin-top: 2rem;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh the page every 30 seconds
    setTimeout(function() {
        location.reload();
    }, 30000);
</script>
{% endblock %}

{% block content %}
        <div class="dashboard-grid">
            <!-- System Status Card -->
            <div class="card">
                <h3>📊 System Status</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-value">{{ (status or {}).get('total_sensors', 0) }}</div>
                        <div class="status-label">Total Sensors</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">{{ (status or {}).get('active_alerts', 0) }}</div>
                        <div class="status-label">Active Alerts</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">{{ (stats or {}).get('total_readings', 0) }}</div>
                        <div class="status-label">Readings (24h)</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">
                            {% if (status or {}).get('monitoring_active') %}
                                ✅ ACTIVE
                            {% else %}
                                ❌ STOPPED
                            {% endif %}
                        </div>
                        <div class="status-label">Monitoring</div>
                    </div>
                </div>
            </div>

            <!-- Temperature & Humidity Overview -->
            <div class="card">
                <h3>🌡️ Environmental Overview (24h)</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-value">{{ (stats or {}).get('avg_temperature', (overview or {}).get('avg_temperature', 'N/A')) }}°C</div>
                        <div class="status-label">Avg Temperature</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">{{ (stats or {}).get('avg_humidity', (overview or {}).get('avg_humidity', 'N/A')) }}%</div>
                        <div class="status-label">Avg Humidity</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">{{ (stats or {}).get('min_temp', (overview or {}).get('min_temp', 'N/A')) }}°C</div>
                        <div class="status-label">Min Temperature</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value">{{ (stats or {}).get('max_temp', (overview or {}).get('max_temp', 'N/A')) }}°C</div>
                        <div class="status-label">Max Temperature</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Alerts -->
        <div class="card">
            <h3>🚨 Active Alerts</h3>
            {% if alerts and alerts|length > 0 %}
                {% for alert in alerts %}
                    <div class="alert alert-{{ (alert.severity or 'medium')|lower }}">
                        <strong>{{ alert.device_id or 'Unknown Device' }}</strong> - {{ alert.alert_type or 'Unknown Alert' }}<br>
                        {{ alert.message or 'No message available' }}<br>
                        <small>{{ alert.created_at or 'Unknown time' }}</small>
                    </div>
                {% endfor %}
            {% else %}
                <p style="color: #28a745; text-align: center; padding: 2rem;">
                    ✅ No active alerts - All systems operating normally
                </p>
            {% endif %}
        </div>

        <!-- Sensor Status -->
        <div class="card">
            <h3>📡 Sensor Status</h3>
            {% if (status or {}).get('sensor_status') %}
                <ul class="sensor-list">
                    {% for sensor in status.sensor_status %}
                        <li class="sensor-item">
                            <div>
                                <strong>{{ sensor.device_id or 'Unknown Device' }}</strong><br>
                                <small>{{ sensor.location or 'Unknown Location' }} - {{ sensor.product_type or 'Unknown Product' }}</small>
                            </div>
                            <div>
                                {% if sensor.last_reading %}
                                    <div>{{ sensor.last_reading.temperature }}°C / {{ sensor.last_reading.humidity }}%</div>
                                    <small>{{ sensor.last_reading.timestamp }}</small>
                                {% endif %}
                                <div class="sensor-status status-{{ sensor.status }}">
                                    {{ sensor.status.upper() }}
                                </div>
                            </div>
                        </li>
                    {% endfor %}
                </ul>
            {% else %}
                <p style="text-align: center; color: #666; padding: 2rem;">
                    No sensors configured. Add sensors to start monitoring.
                </p>
            {% endif %}
        </div>

        <!-- Navigation Links -->
        <div class="nav-links">
            <a href="/sensors">🔧 Manage Sensors</a>
            <a href="/alerts">🚨 View All Alerts</a>
            <a href="/reports">📈 Reports & Analytics</a>
            <a href="/settings">⚙️ Settings</a>
        </div>

        <div class="refresh-info">
            <p>🔄 Dashboard auto-refreshes every 30 seconds</p>
        </div>
{% endblock %}