#!/usr/bin/env python3
"""
Application Configuration Settings
Environment-based configuration management with secure defaults
"""

import os
from datetime import timedelta
from pathlib import Path


class Config:
    """Base configuration class with common settings"""
    
    # Application settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # Database settings
    DATABASE_URL = os.environ.get('DATABASE_URL') or 'sqlite:///data/databases/monitoring.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_RECORD_QUERIES = True
    
    # Security settings
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # API settings
    API_RATE_LIMIT = "1000 per hour"
    API_PAGINATION_DEFAULT = 20
    API_PAGINATION_MAX = 100
    
    # Monitoring settings
    SENSOR_DATA_RETENTION_DAYS = int(os.environ.get('SENSOR_DATA_RETENTION_DAYS', 90))
    ALERT_RETENTION_DAYS = int(os.environ.get('ALERT_RETENTION_DAYS', 365))
    SYSTEM_HEALTH_CHECK_INTERVAL = int(os.environ.get('HEALTH_CHECK_INTERVAL', 300))  # 5 minutes
    
    # Email notification settings
    MAIL_SERVER = os.environ.get('MAIL_SERVER', 'localhost')
    MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER')
    
    # File upload settings
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', 'data/uploads')
    
    # Logging settings
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.environ.get('LOG_FILE', 'logs/app.log')
    LOG_MAX_BYTES = int(os.environ.get('LOG_MAX_BYTES', 10485760))  # 10MB
    LOG_BACKUP_COUNT = int(os.environ.get('LOG_BACKUP_COUNT', 5))
    
    # Redis settings (for caching and background tasks)
    REDIS_URL = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')
    CACHE_TYPE = "redis"
    CACHE_REDIS_URL = REDIS_URL
    
    # Celery settings (for background tasks)
    CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL', REDIS_URL)
    CELERY_RESULT_BACKEND = os.environ.get('CELERY_RESULT_BACKEND', REDIS_URL)
    
    # Monitoring thresholds (default values)
    DEFAULT_TEMP_MIN = float(os.environ.get('DEFAULT_TEMP_MIN', -2.0))
    DEFAULT_TEMP_MAX = float(os.environ.get('DEFAULT_TEMP_MAX', 4.0))
    DEFAULT_HUMIDITY_MIN = float(os.environ.get('DEFAULT_HUMIDITY_MIN', 80.0))
    DEFAULT_HUMIDITY_MAX = float(os.environ.get('DEFAULT_HUMIDITY_MAX', 95.0))
    
    # Alert escalation settings
    ALERT_ESCALATION_ENABLED = os.environ.get('ALERT_ESCALATION_ENABLED', 'true').lower() in ['true', 'on', '1']
    ALERT_COOLDOWN_MINUTES = int(os.environ.get('ALERT_COOLDOWN_MINUTES', 15))
    
    @staticmethod
    def init_app(app):
        """Initialize application with configuration"""
        # Create necessary directories
        os.makedirs('data/databases', exist_ok=True)
        os.makedirs('data/exports', exist_ok=True)
        os.makedirs('data/backups', exist_ok=True)
        os.makedirs('logs', exist_ok=True)


class DevelopmentConfig(Config):
    """Development environment configuration"""
    
    DEBUG = True
    ENV = 'development'
    
    # Development database
    DATABASE_URL = os.environ.get('DEV_DATABASE_URL') or 'sqlite:///data/databases/monitoring_dev.db'
    
    # Relaxed security for development
    SESSION_COOKIE_SECURE = False
    WTF_CSRF_ENABLED = False
    
    # Development logging
    LOG_LEVEL = 'DEBUG'
    
    # Development email (use console backend)
    MAIL_SUPPRESS_SEND = True
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # Development-specific initialization
        app.logger.info("🔧 Development mode enabled")


class TestingConfig(Config):
    """Testing environment configuration"""
    
    TESTING = True
    ENV = 'testing'
    
    # In-memory database for testing
    DATABASE_URL = 'sqlite:///:memory:'
    
    # Disable CSRF for testing
    WTF_CSRF_ENABLED = False
    
    # Disable email sending in tests
    MAIL_SUPPRESS_SEND = True
    
    # Fast password hashing for tests
    BCRYPT_LOG_ROUNDS = 4
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # Testing-specific initialization
        app.logger.info("🧪 Testing mode enabled")


class ProductionConfig(Config):
    """Production environment configuration"""
    
    DEBUG = False
    ENV = 'production'
    
    # Production database (must be set via environment)
    DATABASE_URL = os.environ.get('DATABASE_URL') or \
                   'postgresql://user:password@localhost/monitoring_prod'
    
    # Enhanced security for production
    SESSION_COOKIE_SECURE = True
    WTF_CSRF_ENABLED = True
    
    # Production logging
    LOG_LEVEL = 'WARNING'
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # Production-specific initialization
        app.logger.info("🚀 Production mode enabled")
        
        # Setup error logging to external service
        if os.environ.get('SENTRY_DSN'):
            import sentry_sdk
            from sentry_sdk.integrations.flask import FlaskIntegration
            from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration
            
            sentry_sdk.init(
                dsn=os.environ.get('SENTRY_DSN'),
                integrations=[
                    FlaskIntegration(),
                    SqlalchemyIntegration(),
                ],
                traces_sample_rate=0.1
            )


# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}


def get_config(config_name=None):
    """
    Get configuration class based on environment name
    
    Args:
        config_name (str): Environment name
        
    Returns:
        Config: Configuration class
    """
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'default')
    
    return config.get(config_name, config['default'])
