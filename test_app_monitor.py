#!/usr/bin/env python3
"""
Test script for app_monitor.py
Verifies that the application starts correctly and handles errors gracefully
"""

import requests
import time
import json
import sys
import subprocess
import threading
from datetime import datetime

def test_api_endpoints():
    """Test API endpoints"""
    base_url = "http://localhost:5000"
    
    print("🧪 Testing API Endpoints...")
    
    # Test endpoints
    endpoints = [
        ("/", "Dashboard"),
        ("/api/system/health", "System Health"),
        ("/api/dashboard/data", "Dashboard Data"),
        ("/alerts", "Alerts Page")
    ]
    
    for endpoint, name in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}: OK")
            else:
                print(f"⚠️ {name}: Status {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ {name}: Error - {e}")
    
    # Test sensor data submission
    print("\n🧪 Testing Sensor Data Submission...")
    sensor_data = {
        "device_id": "TEST_SENSOR_001",
        "temperature": 3.5,
        "humidity": 85.0,
        "timestamp": datetime.now().isoformat()
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/data",
            json=sensor_data,
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        if response.status_code == 200:
            print("✅ Sensor Data Submission: OK")
        else:
            print(f"⚠️ Sensor Data Submission: Status {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Sensor Data Submission: Error - {e}")

def simulate_sensor_data():
    """Simulate sensor data for testing"""
    base_url = "http://localhost:5000"
    
    print("📡 Simulating sensor data...")
    
    sensors = [
        {"device_id": "SENSOR_001", "location": "Cold Storage A"},
        {"device_id": "SENSOR_002", "location": "Dairy Refrigerator"},
        {"device_id": "SENSOR_003", "location": "Freezer Unit"}
    ]
    
    for i in range(5):  # Send 5 readings
        for sensor in sensors:
            data = {
                "device_id": sensor["device_id"],
                "temperature": 2.0 + (i * 0.5),  # Gradually increasing temp
                "humidity": 80.0 + (i * 2),      # Gradually increasing humidity
                "location": sensor["location"],
                "timestamp": datetime.now().isoformat()
            }
            
            try:
                response = requests.post(
                    f"{base_url}/api/data",
                    json=data,
                    headers={"Content-Type": "application/json"},
                    timeout=5
                )
                if response.status_code == 200:
                    print(f"✅ Sent data for {sensor['device_id']}: {data['temperature']}°C, {data['humidity']}%")
                else:
                    print(f"⚠️ Failed to send data for {sensor['device_id']}: Status {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"❌ Error sending data for {sensor['device_id']}: {e}")
        
        time.sleep(2)  # Wait 2 seconds between readings

def main():
    """Main test function"""
    print("🧪 Food Monitoring System - Test Suite")
    print("=" * 50)
    
    # Wait for server to start
    print("⏳ Waiting for server to start...")
    time.sleep(3)
    
    # Test basic connectivity
    try:
        response = requests.get("http://localhost:5000", timeout=10)
        print("✅ Server is responding")
    except requests.exceptions.RequestException as e:
        print(f"❌ Server not responding: {e}")
        print("Make sure app_monitor.py is running")
        return
    
    # Run tests
    test_api_endpoints()
    print()
    simulate_sensor_data()
    
    print("\n🎉 Test completed!")
    print("Check the dashboard at http://localhost:5000 to see the results")

if __name__ == "__main__":
    main()
