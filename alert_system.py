import smtplib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from database import DatabaseManager
import json

class AlertSystem:
    """Advanced alert system for monitoring notifications"""

    def __init__(self, config: Dict):
        self.config = config
        self.db = DatabaseManager()
        self.logger = logging.getLogger(__name__)

        # Alert escalation rules
        self.escalation_rules = {
            'HIGH': {
                'immediate_notify': True,
                'escalate_after_minutes': 15,
                'max_escalations': 3
            },
            'MEDIUM': {
                'immediate_notify': True,
                'escalate_after_minutes': 30,
                'max_escalations': 2
            },
            'LOW': {
                'immediate_notify': False,
                'escalate_after_minutes': 60,
                'max_escalations': 1
            }
        }

    def process_alert(self, alert_data: Dict) -> bool:
        """Process a new alert with escalation logic"""
        try:
            # Create alert in database
            alert_id = self._create_alert_record(alert_data)
            if not alert_id:
                return False

            # Apply escalation rules
            severity = alert_data.get('severity', 'MEDIUM')
            rules = self.escalation_rules.get(severity, self.escalation_rules['MEDIUM'])

            if rules['immediate_notify']:
                self._send_immediate_notification(alert_data)

            # Schedule escalation if needed
            self._schedule_escalation(alert_id, alert_data, rules)

            return True

        except Exception as e:
            self.logger.error(f"Error processing alert: {e}")
            return False

    def _create_alert_record(self, alert_data: Dict) -> Optional[int]:
        """Create alert record in database"""
        try:
            success = self.db.insert_alert(
                alert_data['device_id'],
                alert_data['alert_type'],
                alert_data['severity'],
                alert_data['message'],
                alert_data.get('current_value'),
                alert_data.get('threshold_value')
            )

            if success:
                # Get the last inserted alert ID
                import sqlite3
                with sqlite3.connect(self.db.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('SELECT last_insert_rowid()')
                    return cursor.fetchone()[0]

            return None

        except Exception as e:
            self.logger.error(f"Error creating alert record: {e}")
            return None

    def _send_immediate_notification(self, alert_data: Dict):
        """Send immediate notification for high priority alerts"""
        try:
            # Get sensor configuration for email recipients
            sensor_config = self.db.get_sensor_config(alert_data['device_id'])

            recipients = []
            if sensor_config and sensor_config.get('alert_email'):
                recipients.append(sensor_config['alert_email'])

            # Add default recipients from config
            default_recipients = self.config.get('alert_recipients', [])
            recipients.extend(default_recipients)

            # Remove duplicates
            recipients = list(set(recipients))

            for recipient in recipients:
                self._send_email_notification(recipient, alert_data)

        except Exception as e:
            self.logger.error(f"Error sending immediate notification: {e}")

    def _send_email_notification(self, recipient: str, alert_data: Dict):
        """Send email notification"""
        try:
            email_config = self.config.get('email', {})
            if not self._validate_email_config(email_config):
                self.logger.warning("Email configuration incomplete")
                return

            # Create email message
            msg = MIMEMultipart('alternative')
            msg['From'] = email_config['from_email']
            msg['To'] = recipient
            msg['Subject'] = self._generate_email_subject(alert_data)

            # Create HTML and text versions
            html_body = self._generate_html_email_body(alert_data)
            text_body = self._generate_text_email_body(alert_data)

            msg.attach(MIMEText(text_body, 'plain'))
            msg.attach(MIMEText(html_body, 'html'))

            # Send email
            with smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port']) as server:
                server.starttls()
                server.login(email_config['username'], email_config['password'])
                server.send_message(msg)

            self.logger.info(f"Alert email sent to {recipient}")

        except Exception as e:
            self.logger.error(f"Failed to send email to {recipient}: {e}")

    def _validate_email_config(self, email_config: Dict) -> bool:
        """Validate email configuration"""
        required_fields = ['smtp_server', 'smtp_port', 'username', 'password', 'from_email']
        return all(email_config.get(field) for field in required_fields)

    def _generate_email_subject(self, alert_data: Dict) -> str:
        """Generate email subject line"""
        severity = alert_data.get('severity', 'MEDIUM')
        device_id = alert_data.get('device_id', 'Unknown')
        alert_type = alert_data.get('alert_type', 'ALERT')

        return f"[{severity}] Food Monitoring Alert - {device_id} - {alert_type}"

    def _generate_text_email_body(self, alert_data: Dict) -> str:
        """Generate plain text email body"""
        sensor_config = self.db.get_sensor_config(alert_data['device_id'])
        location = sensor_config.get('location', 'Unknown') if sensor_config else 'Unknown'
        product_type = sensor_config.get('product_type', 'Unknown') if sensor_config else 'Unknown'

        body = f"""
FOOD MONITORING SYSTEM ALERT

Alert Details:
- Device ID: {alert_data.get('device_id', 'Unknown')}
- Location: {location}
- Product Type: {product_type}
- Alert Type: {alert_data.get('alert_type', 'Unknown')}
- Severity: {alert_data.get('severity', 'Unknown')}
- Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Message: {alert_data.get('message', 'No message provided')}

Current Value: {alert_data.get('current_value', 'N/A')}
Threshold Value: {alert_data.get('threshold_value', 'N/A')}

IMMEDIATE ACTION REQUIRED:
1. Check the physical storage conditions
2. Verify sensor functionality
3. Take corrective measures if needed
4. Acknowledge this alert in the monitoring dashboard

Dashboard URL: http://localhost:5000

This is an automated message from the Food Monitoring System.
        """
        return body.strip()