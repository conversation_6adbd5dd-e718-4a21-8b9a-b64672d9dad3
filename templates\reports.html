{% extends "base.html" %}

{% block title %}Reports - Food Monitoring System{% endblock %}

{% block extra_css %}
<style>
    .report-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }

    .stats-card h3 {
        color: white;
        margin-bottom: 1rem;
        font-size: 1.2rem;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .stat-item {
        background: rgba(255,255,255,0.1);
        padding: 1rem;
        border-radius: 8px;
        text-align: center;
    }

    .stat-value {
        font-size: 1.8rem;
        font-weight: bold;
        display: block;
        margin-bottom: 0.25rem;
    }

    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .export-section {
        background: #f8f9fa;
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
    }

    .export-form {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        align-items: end;
    }

    .chart-placeholder {
        background: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 3rem;
        text-align: center;
        color: #666;
        margin: 1rem 0;
    }

    .data-table {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 8px;
    }

    .performance-indicator {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .performance-excellent {
        background: #d4edda;
        color: #155724;
    }

    .performance-good {
        background: #d1ecf1;
        color: #0c5460;
    }

    .performance-warning {
        background: #fff3cd;
        color: #856404;
    }

    .performance-poor {
        background: #f8d7da;
        color: #721c24;
    }

    .summary-box {
        background: #e3f2fd;
        border-left: 4px solid #2196f3;
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 0 8px 8px 0;
    }

    .trend-indicator {
        font-size: 1.2rem;
        margin-left: 0.5rem;
    }

    .trend-up { color: #dc3545; }
    .trend-down { color: #28a745; }
    .trend-stable { color: #6c757d; }
</style>
{% endblock %}

{% block extra_js %}
<script>
    function exportData() {
        const form = document.getElementById('exportForm');
        const formData = new FormData(form);
        const params = new URLSearchParams(formData);

        // Show loading indicator
        const button = document.getElementById('exportButton');
        const originalText = button.innerHTML;
        button.innerHTML = '⏳ Exporting...';
        button.disabled = true;

        // Create download link
        const url = `/api/export/data?${params.toString()}`;
        const link = document.createElement('a');
        link.href = url;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Reset button
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        }, 2000);
    }

    function exportSensorData(deviceId) {
        const url = `/api/export/data?device_id=${deviceId}&hours=168`;
        const link = document.createElement('a');
        link.href = url;
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    function generateReport() {
        const reportType = document.getElementById('reportType').value;
        const timeRange = document.getElementById('timeRange').value;

        // This would typically generate a PDF or detailed report
        alert(`Generating ${reportType} report for ${timeRange}. This feature will be implemented in a future version.`);
    }

    function refreshReports() {
        location.reload();
    }
</script>
{% endblock %}

{% block content %}
<div class="card">
    <h2>📈 Reports & Analytics</h2>
    <p>Comprehensive data analysis and reporting for the food monitoring system.</p>

    <div style="display: flex; gap: 1rem; margin-bottom: 2rem; flex-wrap: wrap;">
        <button class="btn" onclick="refreshReports()">🔄 Refresh Data</button>
        <button class="btn btn-success" onclick="generateReport()">📄 Generate Report</button>
        <button class="btn" onclick="window.open('/api/export/data?hours=24', '_blank')">📥 Quick Export (24h)</button>
    </div>
</div>

<!-- Time Period Statistics -->
<div class="report-grid">
    <div class="stats-card">
        <h3>📊 Last 24 Hours</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-value">{{ stats_24h.total_readings or 0 }}</span>
                <div class="stat-label">Total Readings</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">{{ stats_24h.avg_temp or 'N/A' }}°C</span>
                <div class="stat-label">Avg Temperature</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">{{ stats_24h.avg_humidity or 'N/A' }}%</span>
                <div class="stat-label">Avg Humidity</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">{{ stats_24h.min_temp or 'N/A' }}°C / {{ stats_24h.max_temp or 'N/A' }}°C</span>
                <div class="stat-label">Min / Max Temp</div>
            </div>
        </div>
    </div>

    <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
        <h3>📊 Last 7 Days</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-value">{{ stats_7d.total_readings or 0 }}</span>
                <div class="stat-label">Total Readings</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">{{ stats_7d.avg_temp or 'N/A' }}°C</span>
                <div class="stat-label">Avg Temperature</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">{{ stats_7d.avg_humidity or 'N/A' }}%</span>
                <div class="stat-label">Avg Humidity</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">{{ stats_7d.min_temp or 'N/A' }}°C / {{ stats_7d.max_temp or 'N/A' }}°C</span>
                <div class="stat-label">Min / Max Temp</div>
            </div>
        </div>
    </div>

    <div class="stats-card" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);">
        <h3>📊 Last 30 Days</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-value">{{ stats_30d.total_readings or 0 }}</span>
                <div class="stat-label">Total Readings</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">{{ stats_30d.avg_temp or 'N/A' }}°C</span>
                <div class="stat-label">Avg Temperature</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">{{ stats_30d.avg_humidity or 'N/A' }}%</span>
                <div class="stat-label">Avg Humidity</div>
            </div>
            <div class="stat-item">
                <span class="stat-value">{{ stats_30d.min_temp or 'N/A' }}°C / {{ stats_30d.max_temp or 'N/A' }}°C</span>
                <div class="stat-label">Min / Max Temp</div>
            </div>
        </div>
    </div>
</div>

<!-- Data Export Section -->
<div class="card">
    <h3>📥 Data Export</h3>
    <p>Export sensor data for external analysis and reporting.</p>

    <div class="export-section">
        <form id="exportForm" class="export-form">
            <div class="form-group">
                <label>Sensor (optional):</label>
                <select name="device_id">
                    <option value="">All Sensors</option>
                    {% for sensor in sensors %}
                        <option value="{{ sensor.device_id }}">{{ sensor.device_id }} - {{ sensor.location }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="form-group">
                <label>Time Period:</label>
                <select name="hours">
                    <option value="24">Last 24 Hours</option>
                    <option value="168">Last 7 Days</option>
                    <option value="720">Last 30 Days</option>
                    <option value="8760">Last Year</option>
                </select>
            </div>
            <div class="form-group">
                <label>Report Type:</label>
                <select id="reportType">
                    <option value="summary">Summary Report</option>
                    <option value="detailed">Detailed Report</option>
                    <option value="compliance">Compliance Report</option>
                </select>
            </div>
            <div class="form-group">
                <button type="button" id="exportButton" class="btn btn-success" onclick="exportData()">📥 Export CSV</button>
            </div>
        </form>

        <div class="summary-box">
            <h4>📋 Export Information</h4>
            <ul style="color: #666; line-height: 1.6; margin-left: 1rem;">
                <li>CSV format compatible with Excel and other analysis tools</li>
                <li>Includes timestamp, device ID, location, temperature, humidity, and product type</li>
                <li>Data is exported in chronological order (newest first)</li>
                <li>File name includes date and time for easy identification</li>
                <li>Maximum export limit: 10,000 records per file</li>
            </ul>
        </div>
    </div>
</div>

<!-- Recent Data Preview -->
<div class="card">
    <h3>📋 Recent Data Preview (Last 24 Hours)</h3>
    {% if recent_data %}
        <div class="data-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>Timestamp</th>
                        <th>Device ID</th>
                        <th>Location</th>
                        <th>Temperature</th>
                        <th>Humidity</th>
                        <th>Product Type</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    {% for data in recent_data %}
                    <tr>
                        <td>{{ data.timestamp }}</td>
                        <td><strong>{{ data.device_id }}</strong></td>
                        <td>{{ data.location }}</td>
                        <td style="color: #e74c3c; font-weight: bold;">{{ data.temperature }}°C</td>
                        <td style="color: #3498db; font-weight: bold;">{{ data.humidity }}%</td>
                        <td>{{ data.product_type or 'N/A' }}</td>
                        <td>
                            {% if data.temperature >= -2 and data.temperature <= 8 %}
                                <span class="performance-excellent">Normal</span>
                            {% elif data.temperature >= -5 and data.temperature <= 12 %}
                                <span class="performance-good">Acceptable</span>
                            {% elif data.temperature >= -10 and data.temperature <= 15 %}
                                <span class="performance-warning">Warning</span>
                            {% else %}
                                <span class="performance-poor">Critical</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% if recent_data|length >= 50 %}
            <div style="text-align: center; padding: 1rem; color: #666; background: #f8f9fa; border-radius: 8px; margin-top: 1rem;">
                📊 Showing first 50 records. Use export function to get complete data.
            </div>
        {% endif %}
    {% else %}
        <div style="text-align: center; padding: 3rem; color: #666;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">📊</div>
            <h4>No Data Available</h4>
            <p>No sensor data found for the selected period. Check sensor connections and configurations.</p>
        </div>
    {% endif %}
</div>

<!-- Sensor Performance Summary -->
<div class="card">
    <h3>🎯 Sensor Performance Summary</h3>
    {% if sensors %}
        <table class="table">
            <thead>
                <tr>
                    <th>Sensor</th>
                    <th>Location</th>
                    <th>Product Type</th>
                    <th>Status</th>
                    <th>Data Points (24h)</th>
                    <th>Performance</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for sensor in sensors %}
                <tr>
                    <td><strong>{{ sensor.device_id }}</strong></td>
                    <td>{{ sensor.location }}</td>
                    <td>{{ sensor.product_type or 'Not specified' }}</td>
                    <td>
                        {% if sensor.is_active %}
                            <span class="status-online">ACTIVE</span>
                        {% else %}
                            <span class="status-offline">INACTIVE</span>
                        {% endif %}
                    </td>
                    <td>
                        {% set sensor_data_count = recent_data|selectattr('device_id', 'equalto', sensor.device_id)|list|length %}
                        {{ sensor_data_count }}
                        {% if sensor_data_count >= 24 %}
                            <span class="trend-indicator trend-stable">📈</span>
                        {% elif sensor_data_count >= 12 %}
                            <span class="trend-indicator trend-down">📉</span>
                        {% else %}
                            <span class="trend-indicator trend-up">⚠️</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if sensor_data_count >= 24 %}
                            <span class="performance-excellent">Excellent</span>
                        {% elif sensor_data_count >= 12 %}
                            <span class="performance-good">Good</span>
                        {% elif sensor_data_count >= 6 %}
                            <span class="performance-warning">Poor</span>
                        {% else %}
                            <span class="performance-poor">Critical</span>
                        {% endif %}
                    </td>
                    <td>
                        <button class="btn" onclick="exportSensorData('{{ sensor.device_id }}')" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">📥 Export</button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <div style="text-align: center; padding: 3rem; color: #666;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">⚙️</div>
            <h4>No Sensors Configured</h4>
            <p>Go to Settings to add sensors and start monitoring.</p>
            <a href="/settings" class="btn btn-success" style="margin-top: 1rem;">⚙️ Configure Sensors</a>
        </div>
    {% endif %}
</div>

<!-- Charts Placeholder -->
<div class="card">
    <h3>📊 Data Visualization</h3>
    <div class="chart-placeholder">
        <div style="font-size: 3rem; margin-bottom: 1rem;">📈</div>
        <h4>Charts Coming Soon</h4>
        <p>Interactive charts and graphs will be available in a future version.</p>
        <p>Features will include:</p>
        <ul style="text-align: left; display: inline-block; margin-top: 1rem;">
            <li>Temperature and humidity trend lines</li>
            <li>Real-time data visualization</li>
            <li>Comparative analysis between sensors</li>
            <li>Alert frequency charts</li>
            <li>Performance dashboards</li>
        </ul>
    </div>
</div>
{% endblock %}